package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求召唤跨服Boss</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toServer")
public class ReqSummonCrossBossMessage extends AbstractMessage {

	@Override
	public int getId() {
		return 84014;
	}

	/**
	 * MonGenSpecialConfig的id
	 */
	private int cfgId;

	public int getCfgId() {
		return cfgId;
	}

	public void setCfgId(int cfgId) {
		this.cfgId = cfgId;
	}


	@Override
	public boolean read(KryoInput buf) {

		this.cfgId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, cfgId, false);
		return true;
	}
}
