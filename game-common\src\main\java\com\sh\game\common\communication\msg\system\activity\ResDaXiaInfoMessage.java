package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>查询大侠状态信息返回</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDaXiaInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ActivityProtos.ResDaXiaInfo proto;

    private com.sh.game.protos.ActivityProtos.ResDaXiaInfo.Builder builder;

	
	@Override
	public int getId() {
		return 4291;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ActivityProtos.ResDaXiaInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ActivityProtos.ResDaXiaInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ActivityProtos.ResDaXiaInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ActivityProtos.ResDaXiaInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ActivityProtos.ResDaXiaInfo proto) {
        this.proto = proto;
    }

}
