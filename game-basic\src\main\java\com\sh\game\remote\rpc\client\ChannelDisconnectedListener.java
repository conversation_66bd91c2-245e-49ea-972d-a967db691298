package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.RPCConnection;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * Created by 张力 on 2017/6/30.
 */
public class ChannelDisconnectedListener implements ChannelFutureListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ChannelDisconnectedListener.class);

    private RPCConnection connection;

    private int index;


    public ChannelDisconnectedListener(RPCConnection connection, int index) {
        this.connection = connection;
        this.index = index;
    }

    @Override
    public void operationComplete(ChannelFuture future) {
        LOGGER.error("服务器连接断开[{}:{}/{}]", connection.getHost(), connection.getPort(), index);
        connection.getLogin().decrementAndGet();
        connection.getClient().serverUnavailable(connection);
        ModuleClient.EXECUTOR.schedule(() -> connection.getClient().connect(index, connection), 2000, TimeUnit.MILLISECONDS);
    }
}
