package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.BattleGroupBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>通知战斗服创建比赛副本</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqCreateBattleMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125009;
	}
	
	/**
	 * 地图id
	 */
	private int mapId;
	/**
	 * pvpid
	 */
	private int battleId;
	/**
	 * group列表
	 */
	private List<BattleGroupBean> groupList = new ArrayList<>();

	public int getMapId() {
		return mapId;
	}

	public void setMapId(int mapId) {
		this.mapId = mapId;
	}

		public int getBattleId() {
		return battleId;
	}

	public void setBattleId(int battleId) {
		this.battleId = battleId;
	}

		public List<BattleGroupBean> getGroupList() {
		return groupList;
	}

	public void setGroupList(List<BattleGroupBean> groupList) {
		this.groupList = groupList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.mapId = readInt(buf, false);
		this.battleId = readInt(buf, false);
		int groupListLength = readShort(buf);
		for (int groupListI = 0; groupListI < groupListLength; groupListI++) {
			if (readByte(buf) == 0) { 
				this.groupList.add(null);
			} else {
				BattleGroupBean battleGroupBean = new BattleGroupBean();
				battleGroupBean.read(buf);
				this.groupList.add(battleGroupBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, mapId, false);
		this.writeInt(buf, battleId, false);
		writeShort(buf, this.groupList.size());
		for (int groupListI = 0; groupListI < this.groupList.size(); groupListI++) {
			this.writeBean(buf, this.groupList.get(groupListI));
		}
		return true;
	}
}
