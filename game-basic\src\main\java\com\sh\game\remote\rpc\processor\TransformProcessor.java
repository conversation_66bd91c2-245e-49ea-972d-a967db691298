package com.sh.game.remote.rpc.processor;

import com.sh.concurrent.QueueDriver;
import com.sh.concurrent.QueueExecutor;
import com.sh.game.notice.NoticePool;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.server.CommandProcessor;
import com.sh.net.Message;
import com.sh.server.AbstractHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * TransformProcessor
 *
 * <AUTHOR>
 * @date 2020/8/27 17:14
 */
@Slf4j
public class TransformProcessor extends CommandProcessor {
    private final QueueDriver driver = new QueueDriver(new QueueExecutor("transformer-thread", 1, 1),
            "服务器间通信线程", 0, 5000);


    @Override
    public byte id() {
        return 21;
    }

    @Override
    protected QueueDriver getDriver(long key) {
        return driver;
    }

    @Override
    protected NoticePool getNoticePool() {
        throw new UnsupportedOperationException("模块间内部通信线程不支持notice处理");
    }

    @Override
    public void process(AbstractHandler handler) {
        //如果是local的，那么不用走线程池，直接处理
        Message msg = handler.getMsg();

        switch (msg.getId()) {
            case 12: {
                MessageTransform transform = (MessageTransform) msg;
                if (!transform.isEncode()) {
                    handler.run();
                    return;
                }
            }
            break;
            case 13: {
                NoticeTransform transform = (NoticeTransform) msg;
                if (!transform.isEncode()) {
                    handler.run();
                    return;
                }
            }
            break;
        }

        super.process(handler);
    }
}
