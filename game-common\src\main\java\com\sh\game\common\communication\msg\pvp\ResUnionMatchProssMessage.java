package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


import java.util.ArrayList;
import java.util.List;

/**
 * <p>跨服帮战匹配进度</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResUnionMatchProssMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125017;
	}
	
	/**
	 * 帮会id
	 */
	private List<Long> unionId = new ArrayList<>();
	/**
	 * 帮主id
	 */
	private long leaderId;
	/**
	 * 匹配进度,可能用于展示匹配结果
	 */
	private int process;
	/**
	 * 异常原因
	 */
	private String reason;

	public List<Long> getUnionId() {
		return unionId;
	}

	public void setUnionId(List<Long> unionId) {
		this.unionId = unionId;
	}
	public long getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(long leaderId) {
		this.leaderId = leaderId;
	}

		public int getProcess() {
		return process;
	}

	public void setProcess(int process) {
		this.process = process;
	}

		public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		int unionIdLength = readShort(buf);
		for (int unionIdI = 0; unionIdI < unionIdLength; unionIdI++) {
			this.unionId.add(this.readLong(buf));
		}
		this.leaderId = readLong(buf);
		this.process = readInt(buf, false);
		this.reason = readString(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.unionId.size());
		for (int unionIdI = 0; unionIdI < this.unionId.size(); unionIdI++) {
			this.writeLong(buf, this.unionId.get(unionIdI));
		}
		this.writeLong(buf, leaderId);
		this.writeInt(buf, process, false);
		this.writeString(buf, reason);
		return true;
	}
}
