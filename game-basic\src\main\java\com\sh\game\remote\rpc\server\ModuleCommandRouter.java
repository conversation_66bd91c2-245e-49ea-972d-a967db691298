package com.sh.game.remote.rpc.server;

import com.sh.game.remote.rpc.processor.ModuleInternProcessor;
import com.sh.game.remote.rpc.processor.TransformProcessor;
import com.sh.game.server.CommandRouter;

/**
 * ModuleCommandRouter
 *
 * <AUTHOR>
 * @date 2020/8/26 15:51
 */
public class ModuleCommandRouter extends CommandRouter {
    @Override
    public void register() {
        registerProcessor(22, new ModuleInternProcessor());
        registerProcessor(21, new TransformProcessor());
    }
}
