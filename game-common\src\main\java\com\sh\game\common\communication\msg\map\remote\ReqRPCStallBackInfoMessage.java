package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求远程摊位信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqRPCStallBackInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo proto;

    private com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo.Builder builder;

	
	@Override
	public int getId() {
		return 82024;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ReqRPCStallBackInfo proto) {
        this.proto = proto;
    }

}
