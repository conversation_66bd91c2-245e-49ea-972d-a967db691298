package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class EquipPosGemBean extends KryoBean {

	/**
	 * 装备位pos
	 */
	private int equipIndex;
	/**
	 * 该部位已解锁的槽位和灵石id
	 */
	private List<GemBean> beans = new ArrayList<>();

	public int getEquipIndex() {
		return equipIndex;
	}

	public void setEquipIndex(int equipIndex) {
		this.equipIndex = equipIndex;
	}

		public List<GemBean> getBeans() {
		return beans;
	}

	public void setBeans(List<GemBean> beans) {
		this.beans = beans;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.equipIndex = readInt(buf, false);
		int beansLength = readShort(buf);
		for (int beansI = 0; beansI < beansLength; beansI++) {
			if (readByte(buf) == 0) { 
				this.beans.add(null);
			} else {
				GemBean gemBean = new GemBean();
				gemBean.read(buf);
				this.beans.add(gemBean);
			}
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, equipIndex, false);
		writeShort(buf, this.beans.size());
		for (int beansI = 0; beansI < this.beans.size(); beansI++) {
			this.writeBean(buf, this.beans.get(beansI));
		}
		return true;
	}
}
