package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.MatchPlayerBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>游戏服和匹配服重新连接时，更新匹配服池子</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqResetBubbleMatchMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125026;
	}
	
	/**
	 * 系统匹配组
	 */
	private int matchGroup;
	/**
	 * 玩家信息
	 */
	private List<MatchPlayerBean> beans = new ArrayList<>();

	public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

		public List<MatchPlayerBean> getBeans() {
		return beans;
	}

	public void setBeans(List<MatchPlayerBean> beans) {
		this.beans = beans;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.matchGroup = readInt(buf, false);
		int beansLength = readShort(buf);
		for (int beansI = 0; beansI < beansLength; beansI++) {
			if (readByte(buf) == 0) { 
				this.beans.add(null);
			} else {
				MatchPlayerBean matchPlayerBean = new MatchPlayerBean();
				matchPlayerBean.read(buf);
				this.beans.add(matchPlayerBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, matchGroup, false);
		writeShort(buf, this.beans.size());
		for (int beansI = 0; beansI < this.beans.size(); beansI++) {
			this.writeBean(buf, this.beans.get(beansI));
		}
		return true;
	}
}
