package com.sh.game.notice;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/1/13 14:23
 */
@Slf4j
public class NoticeTimeOutChecker implements Runnable{


    private NoticeCenter noticeCenter;

    public NoticeTimeOutChecker(NoticeCenter noticeCenter) {
        this.noticeCenter = noticeCenter;
    }

    @Override
    public void run() {
        try {
            noticeCenter.cleanTimeoutNotice();
        }catch (Exception e) {
            log.error("notice超时检查出错", e);
        }
    }
}
