<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="GameBootStrap" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.sh.game.gate.GameBootstrap" />
    <module name="game-package-game" />
    <option name="PROGRAM_PARAMETERS" value="./conf/game.properties *******" />
    <option name="VM_PARAMETERS" value="     -XX:MetaspaceSize=256m     -XX:MaxMetaspaceSize=256m     -XX:ReservedCodeCacheSize=128m     -XX:+PrintCommandLineFlags     -XX:-OmitStackTraceInFastThrow     -XX:+UseG1GC     -Xms1g     -Xmx1g     -verbose:gc     -XX:+PrintGCDateStamps     -XX:+PrintGCDetails     -Xloggc:./logs/sgc.log     -XX:+HeapDumpOnOutOfMemoryError     -XX:HeapDumpPath=./heapdump.hprof     -Dfile.encoding=UTF-8     -Dgame.script.dev=true     -Dgame.script.findSuperClassInterface=true     -Dgame.log.queueLog=true     -Dgame.log.MsgUnregister=true     -Dlog4j.configurationFile=./release/docker/conf/game/log4j2-game.xml     -Dheart.check.delta=600000" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>