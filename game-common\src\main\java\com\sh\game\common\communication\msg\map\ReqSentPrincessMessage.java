package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求送达公主</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toScene")
public class ReqSentPrincessMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 193002;
	}
	
	/**
	 * 公主唯一怪物id
	 */
	private long mid;

	public long getMid() {
		return mid;
	}

	public void setMid(long mid) {
		this.mid = mid;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.mid = readLong(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, mid);
		return true;
	}
}
