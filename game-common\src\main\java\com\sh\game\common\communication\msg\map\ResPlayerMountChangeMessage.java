package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>玩家骑乘发生变化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPlayerMountChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResPlayerMountChange proto;

    private com.sh.game.protos.MapProtos.ResPlayerMountChange.Builder builder;

	
	@Override
	public int getId() {
		return 67074;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResPlayerMountChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResPlayerMountChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResPlayerMountChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResPlayerMountChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResPlayerMountChange proto) {
        this.proto = proto;
    }

}
