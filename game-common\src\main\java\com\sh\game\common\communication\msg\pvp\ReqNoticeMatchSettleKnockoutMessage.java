package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>通知匹配服结算游戏服的赌池结算</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqNoticeMatchSettleKnockoutMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125028;
	}
	
	/**
	 * 玩家pvpGroup
	 */
	private int pvpGroup;
	/**
	 * 玩家几强
	 */
	private int number;
	/**
	 * 玩家位置
	 */
	private int index;

	public int getPvpGroup() {
		return pvpGroup;
	}

	public void setPvpGroup(int pvpGroup) {
		this.pvpGroup = pvpGroup;
	}

		public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

		public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.pvpGroup = readInt(buf, false);
		this.number = readInt(buf, false);
		this.index = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, pvpGroup, false);
		this.writeInt(buf, number, false);
		this.writeInt(buf, index, false);
		return true;
	}
}
