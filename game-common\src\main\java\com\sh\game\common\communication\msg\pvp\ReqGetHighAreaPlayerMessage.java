package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>洗完牌如果游戏服此时断线，重新启动时向匹配服更新洗完牌的高级赛区玩家</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqGetHighAreaPlayerMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125027;
	}
	
	/**
	 * 系统匹配组
	 */
	private int matchGroup;

	public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.matchGroup = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, matchGroup, false);
		return true;
	}
}
