package com.sh.game.remote.rpc.test;

import com.sh.game.remote.rpc.MessageTransformReceiver;
import com.sh.game.remote.rpc.NoticeTransformReceiver;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.remote.rpc.client.ModuleClientListener;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;

/**
 * ClientMain
 *
 * <AUTHOR>
 * @date 2020/8/27 16:57
 */
public class ClientMain {
    public static void main(String[] args) throws InterruptedException {
        ModuleClient client = new ModuleClient();
        client.setLocalHostId(1);
        client.setModuleClientListener(new ModuleClientListener(){

            @Override
            public void afterLogin(ModuleClient client, RPCConnection connection) {

            }

            @Override
            public void serverUnavailable(ModuleClient client, RPCConnection connection) {

            }

            @Override
            public void serverDestroy(ModuleClient client, RPCConnection connection) {

            }
        });
        client.startModule(0, "127.0.0.1", 20001);

        client.setMessageReceiver(new MessageTransformReceiver() {

            @Override
            public void receive(MessageTransform msg) {
                System.out.println("收到msg transform");
            }
        });

        client.setNoticeReceiver(new NoticeTransformReceiver() {
            @Override
            public void receive(NoticeTransform noticeTransform) {
                System.out.println("收到 notice transform");
            }
        });

        while (true) {
            RPCConnection connection = client.getConnection(203);
            if (connection == null) {
                continue;
            }
            NoticeTransform transform = new NoticeTransform();

            UpdateHpMpNotice notice = new UpdateHpMpNotice();
            notice.setHp(100);
            notice.setMp(100);
            notice.setRid(10001L);
            transform.setNotice(notice);
            transform.setNoticeId(notice.getNoticeID());
            transform.encodeTransform();
            if (connection.available()) {
                connection.activeChannel().writeAndFlush(transform);
            }


            ResHpMpChangeMessage msg = new ResHpMpChangeMessage();
            msg.setHp(100);
            msg.setLid(10001L);
            msg.setMp(234);
            msg.setType(1);

            MessageTransform req = new MessageTransform();
            req.setMessage(msg);
            req.getPidList().add(10001L);
            req.setMessageId(msg.getId());
            req.encodeTransform();

            if (connection.available()) {
                connection.activeChannel().writeAndFlush(req);
            }
            Thread.sleep(2000);
        }
    }
}
