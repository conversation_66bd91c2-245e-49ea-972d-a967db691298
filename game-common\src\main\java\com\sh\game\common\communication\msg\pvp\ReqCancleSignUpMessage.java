package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>跨服帮战取消报名</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqCancleSignUpMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125015;
	}
	
	/**
	 * 帮会id
	 */
	private long uId;
	/**
	 * 帮主id
	 */
	private long leaderId;
	/**
	 * 匹配组
	 */
	private int matchGroup;

	public long getUId() {
		return uId;
	}

	public void setUId(long uId) {
		this.uId = uId;
	}

		public long getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(long leaderId) {
		this.leaderId = leaderId;
	}

		public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.uId = readLong(buf);
		this.leaderId = readLong(buf);
		this.matchGroup = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, uId);
		this.writeLong(buf, leaderId);
		this.writeInt(buf, matchGroup, false);
		return true;
	}
}
