package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>拾取地图道具</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqAutoPickUpMapItemMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem proto;

    private com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem.Builder builder;

	
	@Override
	public int getId() {
		return 67082;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ReqAutoPickUpMapItem proto) {
        this.proto = proto;
    }

}
