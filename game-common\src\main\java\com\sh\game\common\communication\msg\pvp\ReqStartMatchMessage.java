package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.MatchPlayerBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>请求开始匹配</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqStartMatchMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125003;
	}
	
	/**
	 * 组id
	 */
	private long groupId;
	/**
	 * 匹配的玩家列表
	 */
	private List<MatchPlayerBean> playerList = new ArrayList<>();
	/**
	 * pvp类型
	 */
	private int matchType;
	/**
	 * 匹配组（每个组单独匹配）
	 */
	private int matchGroup;

	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

		public List<MatchPlayerBean> getPlayerList() {
		return playerList;
	}

	public void setPlayerList(List<MatchPlayerBean> playerList) {
		this.playerList = playerList;
	}
	public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

		public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.groupId = readLong(buf);
		int playerListLength = readShort(buf);
		for (int playerListI = 0; playerListI < playerListLength; playerListI++) {
			if (readByte(buf) == 0) { 
				this.playerList.add(null);
			} else {
				MatchPlayerBean matchPlayerBean = new MatchPlayerBean();
				matchPlayerBean.read(buf);
				this.playerList.add(matchPlayerBean);
			}
		}
		this.matchType = readInt(buf, false);
		this.matchGroup = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, groupId);
		writeShort(buf, this.playerList.size());
		for (int playerListI = 0; playerListI < this.playerList.size(); playerListI++) {
			this.writeBean(buf, this.playerList.get(playerListI));
		}
		this.writeInt(buf, matchType, false);
		this.writeInt(buf, matchGroup, false);
		return true;
	}
}
