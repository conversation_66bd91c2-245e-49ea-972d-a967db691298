# \u8FDE\u63A5\u6A21\u5F0F\uFF1A0 \u5355\u70B9\u6A21\u5F0F  1 \u54E8\u5175\u6A21\u5F0F  2 cluster\u6A21\u5F0F
redis.mode=0
# redis \u670D\u52A1\u8FDE\u63A5\u5BC6\u7801
redis.password=root
# redis \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4 ms
redis.timeout=10000
# \u4F7F\u7528\u7B2C\u51E0\u4E2A\u5E93\uFF0C\u4E0D\u914D\u7F6E\u9ED8\u8BA40\uFF08\u53EA\u5BF9\u5355\u70B9\u6A21\u5F0F\u3001\u54E8\u5175\u6A21\u5F0F\u6709\u6548\uFF09
redis.dbIndex=0
#-1 \u8868\u793A\u65E0\u9650\u6B21\uFF0C0 \u8868\u793A\u4E0D\u91CD\u8BD5\uFF0CN \u4E3A\u5177\u4F53\u6B21\u6570
redis.retry=-1
#redis\u91CD\u8FDE\u95F4\u9694\u65F6\u95F4
redis.retryInterval=5
# \u8FDE\u63A5\u6C60\u914D\u7F6E\uFF08Apache \u5BF9\u8C61\u8FDE\u63A5\u6C60\uFF09
redis.pool.testOnBorrow=false
redis.pool.testOnReturn=false
redis.pool.testWhileIdle=true
redis.pool.maxTotal=50
redis.pool.maxIdle=30
# \u5355\u8282\u70B9\u6A21\u5F0F\u7684 ip \u7AEF\u53E3\u914D\u7F6E
redis.single.host=127.0.0.1
#redis.single.host=*************
redis.single.port=6379
# \u54E8\u5175\u6A21\u5F0F\u7684 ip \u7AEF\u53E3\u914D\u7F6E \u5217\u8868
redis.sentinel.master=master1
redis.sentinel.nodes=*************:26379,*************:26379
# \u96C6\u7FA4\u6A21\u5F0F\u914D\u7F6E
# \u8FDE\u63A5\u5931\u8D25\u91CD\u8BD5\u6B21\u6570
redis.cluster.maxRetry=5
#\u542F\u7528\u8BE5\u6A21\u5F0F\uFF0C\u5FC5\u987B\u914D\u7F6E\u5927\u4E8E3\u4E2A
redis.cluster.nodes=*************:7000,*************:7001,*************:7002