package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>战斗服之间的心跳</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqBattleHeartMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125006;
	}
	
	/**
	 * 服务器id
	 */
	private int serverId;
	/**
	 * 比赛数
	 */
	private int matchCount;
	/**
	 * 玩家数
	 */
	private int playerCount;

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

		public int getMatchCount() {
		return matchCount;
	}

	public void setMatchCount(int matchCount) {
		this.matchCount = matchCount;
	}

		public int getPlayerCount() {
		return playerCount;
	}

	public void setPlayerCount(int playerCount) {
		this.playerCount = playerCount;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.serverId = readInt(buf, false);
		this.matchCount = readInt(buf, false);
		this.playerCount = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, serverId, false);
		this.writeInt(buf, matchCount, false);
		this.writeInt(buf, playerCount, false);
		return true;
	}
}
