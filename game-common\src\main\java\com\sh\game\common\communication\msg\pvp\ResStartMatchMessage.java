package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>返回开始匹配</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResStartMatchMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125004;
	}
	
	/**
	 * 请求匹配的组
	 */
	private long groupId;
	/**
	 * pvp类型
	 */
	private int matchType;
	/**
	 * 相关提示
	 */
	private String info;

	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

		public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

		public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.groupId = readLong(buf);
		this.matchType = readInt(buf, false);
		this.info = readString(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, groupId);
		this.writeInt(buf, matchType, false);
		this.writeString(buf, info);
		return true;
	}
}
