package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;

import java.util.ArrayList;
import java.util.List;

/**
 * 资质Bean
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-09-25
 **/
public class AptitudeDataBean extends KryoBean {

    private int ziZhiCfgId;

    private int aptitudeValue;

    private List<RandAttributeBean> aptitudeAttrs = new ArrayList<>();

    public int getZiZhiCfgId() {
        return ziZhiCfgId;
    }

    public void setZiZhiCfgId(int ziZhiCfgId) {
        this.ziZhiCfgId = ziZhiCfgId;
    }

    public int getAptitudeValue() {
        return aptitudeValue;
    }

    public void setAptitudeValue(int aptitudeValue) {
        this.aptitudeValue = aptitudeValue;
    }

    public List<RandAttributeBean> getAptitudeAttrs() {
        return aptitudeAttrs;
    }

    public void setAptitudeAttrs(List<RandAttributeBean> aptitudeAttrs) {
        this.aptitudeAttrs = aptitudeAttrs;
    }

    @Override
    public boolean write(KryoOutput buf) {
        this.writeInt(buf, ziZhiCfgId, false);
        this.writeInt(buf, aptitudeValue, false);
        writeShort(buf, this.aptitudeAttrs.size());
        for (int aptitudeAttrI = 0; aptitudeAttrI < this.aptitudeAttrs.size(); aptitudeAttrI++) {
            this.writeBean(buf, this.aptitudeAttrs.get(aptitudeAttrI));
        }
        return false;
    }

    @Override
    public boolean read(KryoInput buf) {
        this.ziZhiCfgId = readInt(buf, false);
        this.aptitudeValue = readInt(buf, false);
        int aptitudeAttrsLength = readShort(buf);
        for (int aptitudeAttrI = 0; aptitudeAttrI < aptitudeAttrsLength; aptitudeAttrI++) {
            if (readByte(buf) == 0) {
                this.aptitudeAttrs.add(null);
            } else {
                RandAttributeBean randAttributeBean = new RandAttributeBean();
                randAttributeBean.read(buf);
                this.aptitudeAttrs.add(randAttributeBean);
            }
        }
        return false;
    }
}
