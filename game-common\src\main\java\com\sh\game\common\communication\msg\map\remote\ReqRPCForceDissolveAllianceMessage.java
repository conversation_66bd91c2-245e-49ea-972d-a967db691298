package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求强制解散联盟</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqRPCForceDissolveAllianceMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance proto;

    private com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance.Builder builder;

	
	@Override
	public int getId() {
		return 82055;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ReqRPCForceDissolveAlliance proto) {
        this.proto = proto;
    }

}
