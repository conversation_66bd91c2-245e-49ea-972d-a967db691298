package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.ModuleSessionValue;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.net.NetworkEventlistener;
import com.sh.server.Session;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleState;
import lombok.extern.slf4j.Slf4j;

/**
 * RPCNetworkEventListener
 *
 * <AUTHOR>
 * @date 2020/8/27 14:10
 */
@Slf4j
public class RPCNetworkEventListener implements NetworkEventlistener {
    @Override
    public void onConnected(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
        if (session == null) {
            session = new Session();
            session.setChannel(channel);
            ModuleSessionValue sv = new ModuleSessionValue();
            session.setValue(sv);
            AttributeUtil.set(channel, ChannelAttrKey.SESSION, session);
            log.info("接收到新的连接：" + channel.toString());
        } else {
            log.error("新连接建立时已存在Session，注意排查原因" + channel.toString());
        }
    }

    @Override
    public void onDisconnected(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
        if (session == null) {
            log.error("连接断开的时候session为null");
            return;
        }

    }

    @Override
    public void onExceptionOccur(ChannelHandlerContext ctx, Throwable cause) {
        log.error("网络发生异常:" + ctx, cause);
    }

    @Override
    public void idle(ChannelHandlerContext ctx, IdleState state) {
        //暂时不做检查
    }
}
