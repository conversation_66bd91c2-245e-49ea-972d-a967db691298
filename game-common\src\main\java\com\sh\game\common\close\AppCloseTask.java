package com.sh.game.common.close;

import com.sh.game.common.communication.msg.system.back.ResCloseServerMessage;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.protos.BackProtos;
import com.sh.server.Session;
import io.netty.channel.ChannelFuture;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AppCloseTask extends Thread {

    private final short sequence;

    private final int source;

    private final Session session;

    private final ICloseTask[] tasks;


    public AppCloseTask(short sequence, int source, Session session, ICloseTask[] tasks) {
        super();
        this.setDaemon(false);
        this.sequence = sequence;
        this.source = source;
        this.session = session;
        this.tasks = tasks;
    }

    @Override
    public void run() {
        if (tasks == null) {
            return;
        }
        for (ICloseTask task : tasks) {
            int code = task.code();
            sendMsg(code, String.format("[%s] 开始执行...", task.getName()));
            long time = TimeUtil.getNowOfMills();
            try {
                task.run();
            } catch (Exception e) {
                sendMsg(code, String.format("[%s] 执行失败: %s", task.getName(), e.getMessage()));
                continue;
            }
            sendMsg(code, String.format("[%s] 执行成功，耗时: %s", task.getName(), TimeUtil.getNowOfMills() - time));
        }

        log.info("退出程序...");
        if (source != 4) {
            System.exit(0);
        }
    }

    private void sendMsg(int code, String msg) {
        log.info(msg);

        if (code >= 0 && source == 1 && session != null) {
            ResCloseServerMessage message = new ResCloseServerMessage();
            message.setSequence(sequence);
            message.setProto(BackProtos.ResCloseServer.newBuilder()
                    .setCode(code)
                    .setInfo(msg)
                    .build());

            if (code > 0) {
                session.sendMessage(message);
            } else {
                try {
                    ChannelFuture future = session.getChannel().writeAndFlush(message).sync();
                    future.get();
                } catch (Exception e) {
                    log.error("发送消息出错: {}", e.getMessage());
                }
            }
        }
    }

}
