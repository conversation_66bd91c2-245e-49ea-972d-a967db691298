# 技术设计文档

## 概述

本设计旨在修复RenderScript.java中砍柴次数恢复机制的冲突问题。通过实施时间窗口锁定机制、状态同步管理和优先级控制，确保登录离线恢复和每秒定时恢复能够协调工作，避免数据冲突和重复恢复。

设计采用最小侵入性原则，在现有代码结构基础上进行优化，确保向后兼容性和系统稳定性。

## 需求映射

### 设计组件可追溯性
每个设计组件对应具体需求：
- **冲突防护机制** → 需求1: 恢复冲突防护（EARS 1.1-1.5）
- **时间计算优化** → 需求2: 恢复时间准确性（EARS 2.1-2.5）
- **上限控制增强** → 需求3: 恢复上限控制（EARS 3.1-3.5）
- **状态管理器** → 需求4: 状态同步管理（EARS 4.1-4.5）
- **日志监控系统** → 需求5: 错误处理和日志（EARS 5.1-5.5）
- **性能优化模块** → 需求6: 性能和稳定性（EARS 6.1-6.5）

### 用户故事覆盖
- **游戏系统需求**: 通过冲突防护和状态管理确保恢复准确性
- **游戏玩家需求**: 通过时间计算优化提供正确的恢复体验
- **运维人员需求**: 通过日志监控系统提供完整的故障诊断能力

## 架构设计

### 高层系统架构

```mermaid
graph TB
    A[玩家登录] --> B[登录恢复控制器]
    C[每秒定时器] --> D[定时恢复控制器]
    B --> E[恢复冲突防护层]
    D --> E
    E --> F[时间计算服务]
    E --> G[上限控制服务]
    F --> H[状态管理器]
    G --> H
    H --> I[砍柴数据模型]
    H --> J[日志监控系统]
```

### 技术栈选择

**核心技术**: 基于现有Java游戏框架
- **并发控制**: Java synchronized关键字 + 时间戳锁定机制
- **状态管理**: 现有RenderData实体扩展
- **日志系统**: 基于现有slf4j日志框架
- **数据持久化**: 现有DataCenter数据管理系统
- **时间处理**: 现有TimeUtil工具类扩展

### 架构决策理由
基于技术研究和需求分析：

- **为什么选择时间窗口锁定**: 最小侵入性，利用现有recordTime机制
- **为什么扩展现有实体**: 保持数据结构一致性，避免迁移风险
- **为什么使用synchronized**: 确保原子操作，符合现有代码风格
- **为什么增强日志系统**: 提供运维可观测性，便于问题诊断

## 数据流设计

### 主要用户流程

#### 登录恢复流程
```mermaid
sequenceDiagram
    participant Player as 玩家
    participant Login as 登录系统
    participant Recovery as 恢复控制器
    participant State as 状态管理器
    participant Data as 数据层
    
    Player->>Login: 玩家登录
    Login->>Recovery: 触发离线恢复
    Recovery->>State: 获取锁定状态
    State->>Recovery: 返回状态信息
    Recovery->>State: 计算并更新恢复数据
    State->>Data: 持久化数据变更
    Recovery->>State: 设置冲突防护锁
    State-->>Login: 恢复完成
    Login-->>Player: 登录成功
```

#### 每秒恢复流程
```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant Recovery as 恢复控制器
    participant State as 状态管理器
    participant Data as 数据层
    
    Timer->>Recovery: 每秒触发
    Recovery->>State: 检查冲突锁状态
    alt 锁定期内
        State-->>Recovery: 跳过执行
        Recovery-->>Timer: 本次跳过
    else 锁定期外
        State->>Recovery: 允许执行
        Recovery->>State: 执行恢复逻辑
        State->>Data: 更新恢复数据
        State-->>Recovery: 执行完成
        Recovery-->>Timer: 本次完成
    end
```

## 组件和接口设计

### 后端服务与方法签名

#### 恢复控制服务
```java
class RecoveryController {
    // 执行登录离线恢复，带冲突防护
    public synchronized boolean executeLoginRecovery(Role role, RenderData renderData);
    
    // 执行每秒定时恢复，检查冲突锁
    public synchronized boolean executeSecondRecovery(Role role, RenderData renderData);
    
    // 检查是否在冲突防护期内
    private boolean isInConflictWindow(int currentTime, int recordTime);
    
    // 计算恢复数量，考虑上限约束
    private int calculateRecoveryAmount(Role role, int timeDiff, int currentAmount, int limit);
}
```

#### 时间计算服务
```java
class TimeCalculationService {
    // 计算离线恢复时间差
    public int calculateOfflineTimeDiff(int logoutTime, int loginTime);
    
    // 验证时间戳有效性
    public boolean validateTimestamp(int timestamp);
    
    // 计算下次恢复时间
    public int calculateNextRecoveryTime(int lastRecoveryTime, int interval);
}
```

#### 状态管理器
```java
class RecoveryStateManager {
    // 设置冲突防护锁
    public void setConflictLock(RenderData renderData, int lockTime);
    
    // 检查冲突锁状态
    public boolean isConflictLocked(RenderData renderData, int currentTime);
    
    // 同步更新恢复状态
    public synchronized void updateRecoveryState(RenderData renderData, int amount, int timestamp);
    
    // 验证数据一致性
    public boolean validateDataConsistency(RenderData renderData);
}
```

### 数据模型设计

#### 扩展的RenderData实体
```java
public class RenderData {
    // 现有字段
    private TwoTuple<Integer, Integer> kanCaiLimit; // 砍柴次数限制 (当前数量, 上次恢复时间)
    private int recordTime; // 记录时间（用作冲突锁）
    
    // 新增字段用于增强冲突控制
    private volatile int conflictLockTime; // 冲突锁时间戳
    private volatile boolean isLoginRecoveryActive; // 登录恢复活跃标记
    private int lastRecoveryType; // 上次恢复类型 (1=登录恢复, 2=每秒恢复)
    
    // 恢复统计信息
    private int totalLoginRecoveries; // 登录恢复总次数
    private int totalSecondRecoveries; // 每秒恢复总次数
    private long lastRecoveryAmount; // 上次恢复数量
}
```

#### 恢复操作记录
```java
public class RecoveryOperation {
    private int operationType; // 操作类型 (1=登录, 2=每秒)
    private int beforeAmount; // 恢复前数量
    private int afterAmount; // 恢复后数量
    private int recoveryAmount; // 恢复数量
    private int timestamp; // 操作时间戳
    private boolean success; // 操作是否成功
    private String errorMessage; // 错误信息
}
```

## 关键实现逻辑

### 冲突防护机制设计
```java
private static final int CONFLICT_WINDOW_SECONDS = 3;

private boolean isInConflictWindow(RenderData renderData, int currentTime) {
    int recordTime = renderData.getRecordTime();
    if (recordTime <= 0) return false;
    
    int timeDiff = Math.abs(currentTime - recordTime);
    return timeDiff <= CONFLICT_WINDOW_SECONDS;
}

private synchronized boolean executeLoginRecovery(Role role, RenderData renderData) {
    int now = TimeUtil.getNowOfSeconds();
    
    // 设置登录恢复活跃标记
    renderData.setLoginRecoveryActive(true);
    renderData.setConflictLockTime(now);
    
    try {
        // 执行恢复逻辑
        boolean success = performOfflineRecovery(role, renderData, now);
        
        // 设置冲突防护锁
        renderData.setRecordTime(now);
        renderData.setLastRecoveryType(1);
        
        return success;
    } finally {
        renderData.setLoginRecoveryActive(false);
    }
}

private synchronized boolean executeSecondRecovery(Role role, RenderData renderData) {
    int now = TimeUtil.getNowOfSeconds();
    
    // 检查冲突锁
    if (isInConflictWindow(renderData, now)) {
        log.info("每秒恢复跳过-冲突锁定期内,roleId:{},recordTime:{},now:{}", 
            role.getId(), renderData.getRecordTime(), now);
        return false;
    }
    
    // 检查登录恢复活跃状态
    if (renderData.isLoginRecoveryActive()) {
        log.info("每秒恢复跳过-登录恢复进行中,roleId:{}", role.getId());
        return false;
    }
    
    // 执行每秒恢复逻辑
    return performSecondRecovery(role, renderData, now);
}
```

### 时间计算优化
```java
private int calculateRecoveryAmount(Role role, int timeDiff, int currentAmount, int maxLimit) {
    if (timeDiff <= 0) return 0;
    
    int recoverTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.KAN_CHAI_RECOVER_TIME);
    int potentialRecovery = timeDiff / recoverTime;
    
    // 考虑当前数量和上限
    int availableSpace = maxLimit - currentAmount;
    int actualRecovery = Math.min(potentialRecovery, availableSpace);
    
    return Math.max(0, actualRecovery);
}
```

## 错误处理和日志

### 日志记录策略
```java
// 恢复成功日志
log.info("砍柴恢复成功-类型:{},roleId:{},恢复前:{},恢复后:{},恢复量:{},时间:{}", 
    recoveryType, roleId, beforeAmount, afterAmount, recoveryAmount, timestamp);

// 冲突检测日志  
log.warn("砍柴恢复冲突检测-roleId:{},登录恢复时间:{},当前时间:{},时间差:{}", 
    roleId, recordTime, currentTime, timeDiff);

// 错误处理日志
log.error("砍柴恢复异常-roleId:{},类型:{},错误:{}", roleId, recoveryType, errorMessage, exception);

// 数据一致性检查日志
log.warn("砍柴数据一致性异常-roleId:{},当前数量:{},上限:{},时间戳:{}", 
    roleId, currentAmount, maxLimit, timestamp);
```

### 异常处理机制
```java
public boolean recoverWithErrorHandling(Role role, RenderData renderData, int recoveryType) {
    RecoveryOperation operation = new RecoveryOperation();
    operation.setOperationType(recoveryType);
    operation.setTimestamp(TimeUtil.getNowOfSeconds());
    
    try {
        // 数据一致性检查
        if (!validateDataConsistency(renderData)) {
            throw new DataConsistencyException("数据一致性检查失败");
        }
        
        int beforeAmount = renderData.getKanCaiLimit().getFirst();
        operation.setBeforeAmount(beforeAmount);
        
        // 执行恢复
        boolean success = executeRecovery(role, renderData, recoveryType);
        
        int afterAmount = renderData.getKanCaiLimit().getFirst();
        operation.setAfterAmount(afterAmount);
        operation.setRecoveryAmount(afterAmount - beforeAmount);
        operation.setSuccess(success);
        
        return success;
        
    } catch (Exception e) {
        operation.setSuccess(false);
        operation.setErrorMessage(e.getMessage());
        log.error("砍柴恢复异常处理", e);
        
        // 尝试数据回滚
        rollbackDataIfNeeded(renderData, operation);
        return false;
        
    } finally {
        // 记录操作日志
        logRecoveryOperation(role, operation);
    }
}
```

## 性能和扩展性

### 性能目标
| 指标 | 目标值 | 测量方式 |
|------|--------|----------|
| 登录恢复响应时间 | < 50ms | 登录流程监控 |
| 每秒恢复检查时间 | < 5ms | 定时器性能监控 |
| 并发处理能力 | > 10000 玩家 | 负载测试 |
| 内存使用增量 | < 1% | 内存监控 |
| CPU 开销增量 | < 2% | CPU 使用率监控 |

### 缓存策略
- **配置缓存**: 恢复间隔、上限配置等静态数据缓存
- **计算结果缓存**: 复杂计算结果短时缓存
- **状态缓存**: 冲突锁状态本地缓存

### 扩展性设计
- **水平扩展**: 每个玩家的恢复逻辑独立，支持分布式部署
- **垂直扩展**: 优化算法复杂度，减少CPU和内存使用
- **配置热更新**: 支持恢复参数动态调整

## 测试策略

### 测试覆盖要求
- **单元测试**: ≥90% 代码覆盖率
- **集成测试**: 所有恢复场景和边界条件
- **并发测试**: 多线程冲突和竞态条件测试
- **性能测试**: 高并发下的性能表现测试

### 测试方法
1. **单元测试**
   - 测试冲突防护机制的各种时间窗口场景
   - 测试时间计算的准确性和边界条件
   - 测试上限控制的各种约束场景

2. **集成测试**
   - 登录恢复与每秒恢复的协调工作
   - 异常情况下的数据一致性保证
   - 日志记录的完整性和准确性

3. **并发压力测试**
   - 大量玩家同时登录的恢复处理
   - 高频率定时器执行的性能影响
   - 内存泄漏和资源回收测试

### 测试用例设计
```java
@Test
public void testConflictPrevention() {
    // 测试登录恢复后3秒内每秒恢复被正确跳过
    Role role = createTestRole();
    RenderData renderData = role.findNormal().getRenderData();
    
    // 执行登录恢复
    assertTrue(recoveryController.executeLoginRecovery(role, renderData));
    
    // 立即尝试每秒恢复，应该被跳过
    assertFalse(recoveryController.executeSecondRecovery(role, renderData));
    
    // 等待4秒后，每秒恢复应该正常执行
    TimeUtil.sleep(4000);
    assertTrue(recoveryController.executeSecondRecovery(role, renderData));
}

@Test 
public void testDataConsistency() {
    // 测试并发修改时的数据一致性
    Role role = createTestRole();
    CountDownLatch latch = new CountDownLatch(2);
    AtomicBoolean loginSuccess = new AtomicBoolean(false);
    AtomicBoolean secondSuccess = new AtomicBoolean(false);
    
    // 模拟同时进行登录恢复和每秒恢复
    new Thread(() -> {
        loginSuccess.set(recoveryController.executeLoginRecovery(role, role.findNormal().getRenderData()));
        latch.countDown();
    }).start();
    
    new Thread(() -> {
        secondSuccess.set(recoveryController.executeSecondRecovery(role, role.findNormal().getRenderData()));
        latch.countDown();
    }).start();
    
    latch.await();
    
    // 验证只有一个恢复成功，数据保持一致
    assertTrue(loginSuccess.get() ^ secondSuccess.get()); // 异或，只有一个为true
    validateDataConsistency(role.findNormal().getRenderData());
}
```

## 部署和监控

### 部署策略
- **灰度部署**: 先在测试服务器验证，再逐步推广到生产环境
- **回滚机制**: 支持快速回滚到原有实现
- **配置隔离**: 新功能通过配置开关控制启用

### 监控指标
```java
// 关键性能指标
private static final Counter loginRecoveryCounter = Counter.build()
    .name("kanchai_login_recovery_total")
    .help("登录恢复总次数")
    .register();

private static final Counter secondRecoveryCounter = Counter.build()
    .name("kanchai_second_recovery_total") 
    .help("每秒恢复总次数")
    .register();

private static final Counter conflictSkippedCounter = Counter.build()
    .name("kanchai_conflict_skipped_total")
    .help("冲突跳过总次数")
    .register();

private static final Histogram recoveryLatency = Histogram.build()
    .name("kanchai_recovery_duration_seconds")
    .help("恢复操作耗时")
    .register();
```

### 告警规则
- **恢复失败率**: 失败率 > 1% 时告警
- **冲突频率**: 冲突跳过率 > 10% 时告警
- **性能异常**: 恢复耗时 > 100ms 时告警
- **数据异常**: 数据一致性检查失败时立即告警