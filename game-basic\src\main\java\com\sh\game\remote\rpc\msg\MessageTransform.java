package com.sh.game.remote.rpc.msg;

import com.sh.net.Message;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import java.util.ArrayList;
import java.util.List;


/**
 * 消息转发
 * <AUTHOR>
 * @date 2018/8/27 18:11
 */
public class MessageTransform extends AbstractMessage {

    /**
     * 玩家id列表
     */
    private List<Long> pidList = new ArrayList<>();

    /**
     * 消息id
     */
    private int messageId;

    private byte[] messages;


    /**
     * 未编码的消息，主要是用于本地地图模式直接内存转发消息的情况
     */
    private Message message;
    private boolean encode = true;

    /**
     * 编码需要传输的消息
     */
    public void encodeTransform() {
        messages = message.encode();
    }


    @Override
    public int getId() {
        return 12;
    }


    @Override
    public boolean write(KryoOutput output) {
        writeShort(output, this.pidList.size());
        for (int i = 0; i < this.pidList.size(); i++) {
            this.writeLong(output, this.pidList.get(i));
        }
        this.writeInt(output, this.messageId, false);
        this.writeBytes(output, messages);
        return false;
    }

    @Override
    public boolean read(KryoInput input) {
        int length = readShort(input);
        for (int i = 0; i < length; i++) {
            this.pidList.add(this.readLong(input));
        }
        this.messageId = this.readInt(input, false);
        this.messages = this.readBytes(input);
        return false;
    }

    public List<Long> getPidList() {
        return pidList;
    }

    public void setPidList(List<Long> pidList) {
        this.pidList = pidList;
    }

    public byte[] getMessages() {
        return messages;
    }

    public void setMessages(byte[] messages) {
        this.messages = messages;
    }

    public int getMessageId() {
        return messageId;
    }

    public void setMessageId(int messageId) {
        this.messageId = messageId;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public boolean isEncode() {
        return encode;
    }

    public void setEncode(boolean encode) {
        this.encode = encode;
    }
}
