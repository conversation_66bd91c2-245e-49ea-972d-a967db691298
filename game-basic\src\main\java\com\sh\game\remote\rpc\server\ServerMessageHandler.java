package com.sh.game.remote.rpc.server;

import com.sh.game.remote.rpc.msg.ClientConnectCompleteMessage;
import com.sh.game.remote.rpc.msg.DestroyMessage;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.server.AbstractHandler;

/**
 * ServerMessageHandler
 *
 * <AUTHOR>
 * @date 2020/8/27 13:21
 */
public class ServerMessageHandler {

    private ModuleNet moduleNet;

    public ServerMessageHandler(ModuleNet moduleNet) {
        this.moduleNet = moduleNet;
    }

    public class RegisterHandler extends AbstractHandler<RegisterMessage> {

        @Override
        public void doAction(RegisterMessage msg) {
            moduleNet.register(msg.getSession(), msg);
        }
    }

    public class <PERSON><PERSON>and<PERSON> extends AbstractHandler<PingMessage> {

        @Override
        public void doAction(PingMessage msg) {
            moduleNet.updateHeart(msg.getSession(), msg);
        }
    }

    class DestroyHandler extends AbstractHandler<DestroyMessage> {

        @Override
        public void doAction(DestroyMessage msg) {
            moduleNet.clientDestroy(msg);
        }
    }

    class ClientConnectCompleteHandler extends AbstractHandler<ClientConnectCompleteMessage> {

        @Override
        public void doAction(ClientConnectCompleteMessage msg) {
            moduleNet.clientConnectComplete(msg);
        }
    }


    public class MessageTransformHandler extends AbstractHandler<MessageTransform> {


        @Override
        public void doAction(MessageTransform msg) {
            //需要一个规则来执行Message
            moduleNet.getMessageReceiver().receive(msg);
        }
    }

    public class NoticeTransformHandler extends AbstractHandler<NoticeTransform> {


        @Override
        public void doAction(NoticeTransform msg) {
            //需要规则来执行Notice
            moduleNet.getNoticeReceiver().receive(msg);
        }
    }
}
