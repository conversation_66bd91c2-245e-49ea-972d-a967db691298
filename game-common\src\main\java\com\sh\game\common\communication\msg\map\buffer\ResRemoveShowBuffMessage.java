package com.sh.game.common.communication.msg.map.buffer;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>移除显示buff</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRemoveShowBuffMessage extends ProtobufMessage {

    private com.sh.game.protos.BufferProtos.ResRemoveShowBuff proto;

    private com.sh.game.protos.BufferProtos.ResRemoveShowBuff.Builder builder;

	
	@Override
	public int getId() {
		return 70006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.BufferProtos.ResRemoveShowBuff.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.BufferProtos.ResRemoveShowBuff.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.BufferProtos.ResRemoveShowBuff.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.BufferProtos.ResRemoveShowBuff getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.BufferProtos.ResRemoveShowBuff proto) {
        this.proto = proto;
    }

}
