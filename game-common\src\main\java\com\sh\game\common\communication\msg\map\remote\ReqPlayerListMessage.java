package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>向远程地图服同步当前在跨服中的玩家</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqPlayerListMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ReqPlayerList proto;

    private com.sh.game.protos.RpcProtos.ReqPlayerList.Builder builder;

	
	@Override
	public int getId() {
		return 82006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ReqPlayerList.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ReqPlayerList.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ReqPlayerList.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ReqPlayerList getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ReqPlayerList proto) {
        this.proto = proto;
    }

}
