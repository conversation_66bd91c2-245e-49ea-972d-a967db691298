# Requirements Document

## 简介
本功能旨在修复RenderScript.java中砍柴次数恢复机制的冲突问题。当前系统存在登录时离线恢复和每秒定时恢复之间的冲突，可能导致重复恢复或时间计算错误，影响游戏平衡性和用户体验。

通过优化恢复逻辑，确保两种恢复机制能够协调工作，提供准确、一致的砍柴次数恢复功能。

## 需求

### 需求1: 恢复冲突防护
**用户故事:** 作为游戏系统，我需要防止登录恢复和每秒恢复之间的冲突，确保砍柴次数恢复的准确性。

#### 验收标准

1. **当** 玩家登录时执行离线恢复 **则** 系统 **应当** 设置恢复锁定标记防止每秒恢复重复执行
2. **当** 登录恢复执行后的3秒内 **则** 每秒恢复机制 **应当** 跳过执行以避免冲突
3. **如果** 系统检测到`recordTime`时间戳与当前时间差小于等于3秒 **则** 每秒恢复 **应当** 暂停执行
4. **当** 登录恢复完成时 **则** 系统 **应当** 更新`kanCaiLimit.second`时间戳为当前时间
5. **当** 系统执行任一种恢复时 **则** 系统 **应当** 确保另一种恢复机制不会同时修改相同数据

### 需求2: 恢复时间准确性
**用户故事:** 作为游戏玩家，我需要系统准确计算离线时间和恢复间隔，确保获得正确的砍柴次数恢复。

#### 验收标准

1. **当** 计算离线恢复时 **则** 系统 **应当** 基于玩家的实际登出时间和当前登录时间计算时间差
2. **当** 执行每秒恢复时 **则** 系统 **应当** 检查距离上次恢复是否已达到恢复间隔时间
3. **如果** 离线时间计算结果为负数或零 **则** 系统 **应当** 跳过离线恢复执行
4. **当** 系统计算恢复数量时 **则** 系统 **应当** 使用配置的恢复间隔时间进行准确计算
5. **如果** 恢复时间戳异常或缺失 **则** 系统 **应当** 记录错误日志并使用安全默认值

### 需求3: 恢复上限控制
**用户故事:** 作为游戏系统，我需要确保恢复后的砍柴次数不超过配置的上限值，维护游戏平衡。

#### 验收标准

1. **当** 执行任何恢复操作时 **则** 系统 **应当** 检查当前砍柴次数是否已达到上限
2. **如果** 当前砍柴次数已达到上限 **则** 系统 **应当** 跳过恢复执行并记录警告日志
3. **当** 恢复计算结果超过上限时 **则** 系统 **应当** 将砍柴次数设置为上限值而非计算结果
4. **当** 系统计算上限值时 **则** 系统 **应当** 包含建筑配置和卡牌加成的总和
5. **如果** 上限配置缺失或异常 **则** 系统 **应当** 使用默认安全上限值

### 需求4: 状态同步管理  
**用户故事:** 作为游戏系统，我需要正确维护恢复状态和时间戳，确保各个恢复机制的协调工作。

#### 验收标准

1. **当** 登录恢复执行时 **则** 系统 **应当** 同步更新`recordTime`和`kanCaiLimit.second`时间戳
2. **当** 每秒恢复执行时 **则** 系统 **应当** 只更新`kanCaiLimit.second`时间戳
3. **当** 任一恢复执行后 **则** 系统 **应当** 持久化保存相关数据状态
4. **如果** 时间戳更新失败 **则** 系统 **应当** 记录错误日志并回滚恢复操作
5. **当** 系统重启或异常恢复时 **则** 状态数据 **应当** 保持一致性和完整性

### 需求5: 错误处理和日志
**用户故事:** 作为系统运维人员，我需要清晰的日志记录和错误处理，以便监控和诊断恢复功能的运行状态。

#### 验收标准

1. **当** 恢复冲突被检测到时 **则** 系统 **应当** 记录详细的冲突信息到日志中
2. **当** 恢复成功执行时 **则** 系统 **应当** 记录恢复前后的数量变化和执行时间
3. **如果** 恢复过程中发生异常 **则** 系统 **应当** 记录错误详情并确保数据一致性
4. **当** 检测到异常时间戳或数据状态时 **则** 系统 **应当** 记录警告日志并采用安全处理策略  
5. **当** 恢复被跳过时 **则** 系统 **应当** 记录跳过原因和相关上下文信息

### 需求6: 性能和稳定性
**用户故事:** 作为游戏系统，我需要确保恢复功能高效运行，不影响游戏的整体性能和稳定性。

#### 验收标准

1. **当** 大量玩家同时登录时 **则** 离线恢复处理 **应当** 在合理时间内完成而不阻塞其他操作
2. **当** 每秒定时器执行时 **则** 恢复检查 **应当** 快速完成以避免影响游戏帧率
3. **如果** 恢复计算复杂度较高 **则** 系统 **应当** 采用优化算法减少计算时间
4. **当** 系统负载较高时 **则** 恢复功能 **应当** 保持稳定运行不出现卡顿或崩溃
5. **当** 恢复数据量较大时 **则** 系统 **应当** 采用批量处理或分批更新策略