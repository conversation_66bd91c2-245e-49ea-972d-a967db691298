package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class IdentifyAttributeBean extends KryoBean {

	/**
	 * 属性Bean
	 */
	private RandAttributeBean attrBean;
	/**
	 * 位置，从0开始
	 */
	private int pos;

	public RandAttributeBean getAttrBean() {
		return attrBean;
	}

	public void setAttrBean(RandAttributeBean attrBean) {
		this.attrBean = attrBean;
	}

		public int getPos() {
		return pos;
	}

	public void setPos(int pos) {
		this.pos = pos;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		if (readByte(buf) != 0) {
			RandAttributeBean randAttributeBean = new RandAttributeBean();
			randAttributeBean.read(buf);
			this.attrBean = randAttributeBean;
		}
		this.pos = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeBean(buf, attrBean);
		this.writeInt(buf, pos, false);
		return true;
	}
}
