package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class MatchPlayerBean extends KryoBean {

	/**
	 * 玩家id
	 */
	private long playerId;
	/**
	 * 玩家名
	 */
	private String roleName;
	/**
	 * 队伍匹配参数
	 */
	private int fightPower;
	/**
	 * 性别
	 */
	private int sex;
	/**
	 * 职业
	 */
	private int career;
	/**
	 * 玩家所在服务器id
	 */
	private int hostId;
	/**
	 * 玩家所在服务名称
	 */
	private String serverName;
	/**
	 * 胜率
	 */
	private int successRate;
	/**
	 * 玩家段位参数
	 */
	private int duanWei;

	public long getPlayerId() {
		return playerId;
	}

	public void setPlayerId(long playerId) {
		this.playerId = playerId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public int getFightPower() {
		return fightPower;
	}

	public void setFightPower(int fightPower) {
		this.fightPower = fightPower;
	}

	public int getSex() {
		return sex;
	}

	public void setSex(int sex) {
		this.sex = sex;
	}

	public int getCareer() {
		return career;
	}

	public void setCareer(int career) {
		this.career = career;
	}

	public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public int getSuccessRate() {
		return successRate;
	}

	public void setSuccessRate(int successRate) {
		this.successRate = successRate;
	}

	public int getDuanWei() {
		return duanWei;
	}

	public void setDuanWei(int duanWei) {
		this.duanWei = duanWei;
	}


	@Override
	public boolean read(KryoInput buf) {

		this.playerId = readLong(buf);
		this.roleName = readString(buf);
		this.fightPower = readInt(buf, false);
		this.sex = readInt(buf, false);
		this.career = readInt(buf, false);
		this.hostId = readInt(buf, false);
		this.serverName = readString(buf);
		this.successRate = readInt(buf, false);
		this.duanWei = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, playerId);
		this.writeString(buf, roleName);
		this.writeInt(buf, fightPower, false);
		this.writeInt(buf, sex, false);
		this.writeInt(buf, career, false);
		this.writeInt(buf, hostId, false);
		this.writeString(buf, serverName);
		this.writeInt(buf, successRate, false);
		this.writeInt(buf, duanWei, false);
		return true;
	}
}
