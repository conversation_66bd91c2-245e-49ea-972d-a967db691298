package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class GemBean extends KryoBean {

	/**
	 * 槽位
	 */
	private int equipIndex;
	/**
	 * 灵石id
	 */
	private int stoneCfgId;

	public int getEquipIndex() {
		return equipIndex;
	}

	public void setEquipIndex(int equipIndex) {
		this.equipIndex = equipIndex;
	}

		public int getStoneCfgId() {
		return stoneCfgId;
	}

	public void setStoneCfgId(int stoneCfgId) {
		this.stoneCfgId = stoneCfgId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.equipIndex = readInt(buf, false);
		this.stoneCfgId = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, equipIndex, false);
		this.writeInt(buf, stoneCfgId, false);
		return true;
	}
}
