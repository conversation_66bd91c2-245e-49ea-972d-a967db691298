package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


import java.util.ArrayList;
import java.util.List;

/**
 * <p>匹配进度信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResMatchProgressMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125011;
	}
	
	/**
	 * 组id
	 */
	private List<Long> groupId = new ArrayList<>();
	/**
	 * 进度
	 */
	private int process;
	/**
	 * 信息
	 */
	private String info;

	public List<Long> getGroupId() {
		return groupId;
	}

	public void setGroupId(List<Long> groupId) {
		this.groupId = groupId;
	}
	public int getProcess() {
		return process;
	}

	public void setProcess(int process) {
		this.process = process;
	}

		public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		int groupIdLength = readShort(buf);
		for (int groupIdI = 0; groupIdI < groupIdLength; groupIdI++) {
			this.groupId.add(this.readLong(buf));
		}
		this.process = readInt(buf, false);
		this.info = readString(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.groupId.size());
		for (int groupIdI = 0; groupIdI < this.groupId.size(); groupIdI++) {
			this.writeLong(buf, this.groupId.get(groupIdI));
		}
		this.writeInt(buf, process, false);
		this.writeString(buf, info);
		return true;
	}
}
