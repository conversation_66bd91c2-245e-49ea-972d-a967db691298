package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>对怪物的采集次数发生变化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResMonsterCollectedUpdateMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate proto;

    private com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate.Builder builder;

	
	@Override
	public int getId() {
		return 67077;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResMonsterCollectedUpdate proto) {
        this.proto = proto;
    }

}
