package com.sh.game.remote.rpc.msg;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * 心跳消息
 * <AUTHOR>
 * @date 2018/8/27 18:11
 */
public class PingMessage extends AbstractMessage {

    @Override
    public int getId() {
        return 14;
    }


    @Override
    public boolean read(KryoInput buf) {


        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {


        return true;
    }
}
