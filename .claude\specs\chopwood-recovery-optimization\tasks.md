# 砍柴次数恢复优化实施计划

## 实施任务清单

### 1. 增强登录恢复逻辑
- [ ] 1.1 修改`calLiXianKanChaiCount()`方法，添加恢复前计数追踪
  - 在恢复计算前记录`beforeCount = kanCaiLimit.getFirst()`
  - 引用需求1.4：记录恢复前数量、恢复后数量、恢复的具体数量
- [ ] 1.2 实现登录恢复标记机制
  - 在成功恢复后设置`renderData.setRecordTime(nowOfSeconds)`
  - 引用需求3.1：使用RecordTime字段标记登录恢复执行时间
- [ ] 1.3 添加登录恢复详细日志记录
  - 当实际发生恢复时记录INFO级别日志
  - 包含玩家ID、恢复前数量、恢复后数量、恢复数量
  - 引用需求5.1：登录恢复成功时记录详细日志

### 2. 实现每秒定时器冲突避免机制
- [ ] 2.1 在`onRoleSecond()`方法中添加登录恢复检测逻辑
  - 检查`renderData.getRecordTime() > 0`
  - 计算时间差：`Math.abs(renderData.getRecordTime() - now)`
  - 引用需求2.1：检查登录恢复是否刚刚执行过
- [ ] 2.2 实现冲突时跳过恢复机制
  - 当时间差小于等于3秒时，直接返回跳过恢复
  - 确保跳过不影响后续正常恢复逻辑
  - 引用需求2.2：登录恢复在近期已执行时跳过每秒恢复
- [ ] 2.3 保持正常每秒恢复逻辑完整性
  - 确保无冲突时按原逻辑执行恢复
  - 保持所有现有的上限检查和时间间隔验证
  - 引用需求2.3：没有近期登录恢复时按正常逻辑执行

### 3. 编写单元测试验证核心功能
- [ ] 3.1 创建登录恢复优先级测试
  - 模拟玩家离线场景，验证登录时恢复计算正确性
  - 验证recordTime标记设置正确
  - 测试文件：`RenderScriptLoginRecoveryTest.java`
- [ ] 3.2 创建冲突避免机制测试
  - 测试登录恢复后立即触发每秒恢复的跳过行为
  - 验证3秒时间窗口的边界条件
  - 测试文件：`RenderScriptConflictAvoidanceTest.java`
- [ ] 3.3 创建边界条件测试
  - 测试恢复次数达到上限的情况
  - 测试异常时间戳处理
  - 测试零离线时间的处理

### 4. 实现数据一致性保护
- [ ] 4.1 添加恢复次数上限验证
  - 确保恢复后的次数不超过`config.getShangxian() + cardAddKanChaiLimit(role)`
  - 在登录恢复中使用`Math.min()`确保不超上限
  - 引用需求4.1：不影响砍柴次数上限检查逻辑
- [ ] 4.2 实现时间异常处理保护
  - 检测时间回退或异常情况
  - 当检测到异常时重置recordTime为0
  - 添加相应的WARNING级别日志
- [ ] 4.3 验证现有功能兼容性
  - 确保`reqClickIncome()`砍柴消耗逻辑不受影响
  - 确保`useItemEnergy()`道具恢复逻辑不受影响
  - 引用需求4.2、4.3：保持现有消耗和使用逻辑不变

### 5. 优化日志记录系统
- [ ] 5.1 实现登录恢复成功日志
  - 格式：包含roleId、roleName、恢复前后数量、恢复数量
  - 级别：INFO级别，便于运营监控
  - 引用需求5.1：登录恢复成功时记录INFO级别日志
- [ ] 5.2 添加异常情况错误日志
  - 时间异常、数据异常等情况的ERROR级别日志
  - 包含详细错误信息和玩家上下文
  - 引用需求5.3：异常情况下记录ERROR级别日志
- [ ] 5.3 保持日志格式一致性
  - 遵循现有日志格式标准
  - 确保日志可被现有监控系统正确解析
  - 引用需求5.4：日志格式与现有系统保持一致

### 6. 集成测试和验证
- [ ] 6.1 执行正常登录流程集成测试
  - 测试完整的登录→离线恢复→每秒跳过→正常恢复流程
  - 验证数据库更新的正确性
  - 验证客户端消息同步正常
- [ ] 6.2 执行长时间离线场景测试
  - 模拟超过最大离线时间的恢复计算
  - 验证不会超过配置的上限
  - 测试不同离线时长的恢复准确性
- [ ] 6.3 执行频繁登录压力测试
  - 测试短时间内多次登录的处理
  - 验证每次恢复计算的独立性和正确性
  - 确保无性能问题或数据竞争

### 7. 性能优化和监控
- [ ] 7.1 验证数据库更新性能影响
  - 对比修改前后的`DataCenter.updateData()`调用频率
  - 确保数据库写入性能在可接受范围内
  - 监控数据库连接池使用情况
- [ ] 7.2 测试每秒定时器性能变化
  - 统计每秒定时器的平均执行时间变化
  - 验证跳过逻辑不会增加显著性能开销
  - 测试大量在线玩家时的性能表现
- [ ] 7.3 建立监控指标
  - 登录恢复执行次数统计
  - 每秒恢复跳过次数统计
  - 异常情况发生频率监控

### 8. 代码审查和文档更新
- [ ] 8.1 进行代码审查确保质量
  - 检查代码逻辑正确性和异常处理完整性
  - 验证代码风格符合项目规范
  - 确保没有引入安全漏洞或性能问题
- [ ] 8.2 更新相关技术文档
  - 更新系统设计文档中的恢复机制描述
  - 添加新增配置参数的说明
  - 更新故障排查手册中的相关章节