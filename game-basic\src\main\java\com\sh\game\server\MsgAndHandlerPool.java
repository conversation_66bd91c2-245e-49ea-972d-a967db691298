package com.sh.game.server;

import com.sh.net.Message;
import com.sh.net.MessagePool;
import com.sh.server.AbstractHandler;
import com.sh.server.AbstractMessage;
import com.sh.server.HandlerPool;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/1/7 15:14
 */
@Slf4j
public class MsgAndHandlerPool implements MessagePool, HandlerPool {

    protected Map<Integer, Class<? extends AbstractMessage>> msgPool = new HashMap<>();

    protected Map<Integer, Byte> queueIdMap = new HashMap<>();

    protected Map<Integer, Class<? extends AbstractHandler<? extends AbstractMessage>>> handlerPool = new HashMap<>();


    @Override
    public AbstractHandler<? extends AbstractMessage> getHandler(int messageId) {
        Class<? extends AbstractHandler<? extends AbstractMessage>> clazz = handlerPool.get(messageId);
        if (clazz != null) {
            try {
                AbstractHandler<? extends AbstractMessage> handler = clazz.getDeclaredConstructor().newInstance();
                return handler;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    @Override
    public Message getMessage(int messageId) {
        Class<? extends AbstractMessage> clazz = msgPool.get(messageId);
        if (clazz != null) {
            try {
                AbstractMessage msg = clazz.getDeclaredConstructor().newInstance();
                int queueId = queueIdMap.get(messageId);
                msg.setProcessorId(queueId);
                return msg;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }


    public <T extends AbstractMessage> void register(T message, Class<? extends AbstractHandler<T>> handlerClazz, byte queueId) {

        if (this.msgPool.containsKey(message.getId())) {
            Class<? extends AbstractMessage> existMsg = this.msgPool.get(message.getId());
            throw new RuntimeException("消息号[{"+message.getId()+"}]注册重复,已注册：{"+existMsg.getName()+"},准备注册：{"+message.getClass().getName()+"}");
        }

        msgPool.put(message.getId(), message.getClass());
        queueIdMap.put(message.getId(), queueId);
        handlerPool.put(message.getId(), handlerClazz);
    }



    public void register(MsgAndHandlerPool pool) {

        pool.msgPool.forEach((k, v) -> {
            Class<? extends AbstractHandler<? extends AbstractMessage>> handlerClazz = pool.handlerPool.get(k);
            byte queueId = pool.queueIdMap.get(k);

            if (this.msgPool.containsKey(k)) {
                Class<? extends AbstractMessage> existMsg = this.msgPool.get(k);
                throw new RuntimeException("消息号[{"+k+"}]注册重复,已注册：{"+existMsg.getName()+"},准备注册：{"+v.getName()+"}");
            }

            this.handlerPool.put(k, handlerClazz);
            this.msgPool.put(k, v);
            this.queueIdMap.put(k, queueId);
        });

    }


    protected <T extends AbstractMessage> void register(T message, Class<? extends AbstractHandler<T>> handlerClazz) {
        register(message, handlerClazz, (byte)0);
    }
}
