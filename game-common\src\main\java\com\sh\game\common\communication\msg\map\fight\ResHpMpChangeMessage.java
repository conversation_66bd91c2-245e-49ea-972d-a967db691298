package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>血量蓝量发生变化（用于单独的通知血量，比如说buffer伤害等等）</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResHpMpChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ResHpMpChange proto;

    private com.sh.game.protos.FightProtos.ResHpMpChange.Builder builder;

	
	@Override
	public int getId() {
		return 69004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ResHpMpChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ResHpMpChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ResHpMpChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ResHpMpChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ResHpMpChange proto) {
        this.proto = proto;
    }

}
