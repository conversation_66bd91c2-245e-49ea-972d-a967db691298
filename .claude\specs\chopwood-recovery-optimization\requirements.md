# 砍柴次数恢复优化功能需求

## 功能简介

优化砍柴次数恢复机制，确保登录时的离线恢复优先执行，避免每秒定时器与登录恢复产生冲突，防止重复恢复或恢复逻辑混乱的问题。

## 需求详情

### 1. 登录优先恢复机制

**用户故事**: 作为一个游戏玩家，我希望在登录时能优先获得离线期间应得的砍柴次数恢复，这样我就能确保获得准确的离线收益。

**验收标准**:
1.1 系统应当在玩家登录时立即计算并执行离线砍柴次数恢复
1.2 登录恢复必须基于玩家的离线时长和恢复速率进行准确计算
1.3 登录恢复执行后，系统应当设置相应标记以标识恢复已完成
1.4 登录恢复过程应当记录详细日志，包括恢复前数量、恢复后数量、恢复的具体数量

### 2. 每秒定时器冲突避免

**用户故事**: 作为系统开发者，我希望每秒定时器不会与登录恢复产生冲突，这样就能确保砍柴次数恢复的准确性和一致性。

**验收标准**:
2.1 每秒定时器在执行砍柴次数恢复前，必须检查登录恢复是否刚刚执行过
2.2 如果登录恢复在近期（3秒内）已执行，每秒定时器应当跳过本次恢复
2.3 如果没有检测到近期的登录恢复，每秒定时器应当按正常逻辑执行恢复
2.4 每秒定时器的跳过行为不应当影响后续正常的定时恢复逻辑

### 3. 恢复状态追踪

**用户故事**: 作为系统管理员，我希望能够追踪砍柴次数恢复的执行状态，这样就能监控系统运行情况和排查问题。

**验收标准**:
3.1 系统应当使用现有的RecordTime字段来标记登录恢复的执行时间
3.2 登录时应当重置RecordTime为当前时间戳，表示登录恢复已执行
3.3 每秒定时器应当通过检查RecordTime与当前时间的差值来判断是否跳过恢复
3.4 时间差值的判断阈值应当设置为3秒，既能避免冲突又不会影响正常恢复

### 4. 兼容性保证

**用户故事**: 作为产品负责人，我希望新的恢复机制能够与现有系统完全兼容，这样就不会影响其他功能的正常运行。

**验收标准**:
4.1 修改不应当影响砍柴次数上限检查逻辑
4.2 修改不应当影响砍柴次数的消耗和使用逻辑  
4.3 修改不应当影响快速砍柴等其他相关功能
4.4 修改应当保持现有的数据结构和接口不变

### 5. 错误处理和日志

**用户故事**: 作为系统运维人员，我希望能够通过日志了解砍柴恢复的详细执行情况，这样就能及时发现和解决问题。

**验收标准**:
5.1 登录恢复成功时应当记录INFO级别日志，包含玩家信息和恢复详情
5.2 每秒定时器跳过恢复时应当有适当的日志记录（可选，避免日志过多）
5.3 异常情况下应当记录ERROR级别日志，包含详细的错误信息
5.4 日志格式应当与现有系统保持一致，便于日志分析和监控