package com.sh.game.remote.rpc.client;

import com.sh.game.remote.local.LocalRPCConnection;
import com.sh.game.remote.rpc.*;
import com.sh.game.remote.rpc.msg.*;
import com.sh.net.MessageDecoder;
import com.sh.net.MessageEncoder;
import com.sh.net.MessageExecutor;
import com.sh.net.NetworkConsumer;
import com.sh.server.Session;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;

/**
 * ModuleClient
 *
 * <AUTHOR>
 * @date 2020/8/26 17:43
 */
@Slf4j
public class ModuleClient {
    public static final ScheduledExecutorService EXECUTOR = Executors.newSingleThreadScheduledExecutor(r -> new Thread(r, "module-client"));

    private final NioEventLoopGroup group;

    private final Bootstrap bootstrap;

    private final NetworkConsumer consumer;

    private int localHostId;

    private ModuleClientListener moduleClientListener;

    MessageTransformReceiver messageReceiver;

    NoticeTransformReceiver noticeReceiver;

    //一个Module可能分散在不同服务器上，所以会有多个Connection
    private Map<Integer, RPCConnection> connectionMap = new ConcurrentHashMap<>();

    protected Map<String, RPCConnection> ipMap = new ConcurrentHashMap<>();

    protected Map<Integer, List<RPCConnection>> typeMap = new ConcurrentHashMap<>();

    protected String noticeSign;

    /**
     * 是否调试模式
     */
    private boolean isDebug;

    public ModuleClient() {
        group = new NioEventLoopGroup(2);

        bootstrap = new Bootstrap();
        bootstrap.group(group).channel(NioSocketChannel.class);
        bootstrap.option(ChannelOption.TCP_NODELAY, true);

        consumer = new RPCClientConsumer(ModuleClient.this);

        bootstrap.handler(new ChannelInitializer<SocketChannel>() {
            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                ChannelPipeline pip = ch.pipeline();
                pip.addLast("NettyMessageDecoder", new MessageDecoder(new RPCMessagePool(Collections.emptyMap())));
                pip.addLast("NettyMessageEncoder", new MessageEncoder());
                pip.addLast("NettyMessageExecutor", new MessageExecutor(
                        consumer, new RPCNetworkEventListener(), false));
            }
        });

        EXECUTOR.scheduleWithFixedDelay(this::checkHeart, 2000, 2000, TimeUnit.MILLISECONDS);
    }


    /**
     * 启动一个本地Module
     *
     * @param type
     */
    public RPCConnection createLocalConnection(int type, int port) {

        //直接创建一个本地connection
        LocalRPCConnection connection = new LocalRPCConnection();
        connection.setType(type);
        connection.setHost("localhost");
        connection.setPort(port);
        connection.setHostId(this.getLocalHostId());
        connection.setPeerConsumer(consumer);

        return connection;
    }

    public void startModule(int type, RPCConnection conn) {
        conn.setType(type);
        ipMap.put("localhost" + ":" + conn.getPort(), conn);

        //1. 注册进去
        //2. 返回
        //3. 走同步地图的流程
        afterLogin(conn);
    }


    /**
     * 启动客户端
     */
    public void startModule(int type, String host, int port) {
        if (ipMap.containsKey(host + ":" + port)) {
            log.error("repeated connect module. {} {} {}", type, host, port);
            return;
        }

        RPCConnection connection = new RPCConnection();
        connection.setType(type);
        connection.setHost(host);
        connection.setPort(port);
        connection.setClient(this);
        ipMap.put(host + ":" + port, connection);
        connect(0, connection);
    }

    /**
     * 停止一个服务器的客户端
     *
     * @param host
     * @param port
     */
    public void stop(String host, int port) {
        RPCConnection conn = ipMap.remove(host + ":" + port);
        if (conn == null) {
            log.error("移除远程链接失败，host {} port {} ipMap内容 {}", host, port, JSON.toJSONString(ipMap));
            return;
        }
        log.info("移除远程链接成功，host {} port {} ipMap内容 {}", host, port, JSON.toJSONString(ipMap));

        conn.setDestroy(true);
        connectionMap.remove(conn.getHostId());
        log.info("移除远程链接后，connectionMap内容 {}", JSON.toJSONString(connectionMap));

        List<RPCConnection> rpcConnections = typeMap.get(conn.getType());
        if (rpcConnections != null) {
            boolean isSuccess = rpcConnections.remove(conn);
            log.info("移除rpc连接#移除：{}，移除成功：{} typeMap内容 {}", conn, isSuccess, JSON.toJSONString(typeMap));
            if (rpcConnections.isEmpty()) {
                typeMap.remove(conn.getType());
            }
        }

        if (conn instanceof LocalRPCConnection) {
            //本地模块直接返回
            return;
        }
        if (!conn.isRunning()) {
            return;
        }

        //发送关闭信息给对方
        DestroyMessage msg = new DestroyMessage();
        msg.setModuleId(conn.getHostId());
        ChannelFuture channelFuture = conn.activeChannel().writeAndFlush(msg);
        try {
            channelFuture.get(5000, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("发送消息给对端出错", e);
        }
        conn.close();

    }

    /**
     * 停止所有的连接
     */
    public void stop() {
        RPCConnection[] rpcConnections = ipMap.values().toArray(new RPCConnection[0]);
        for (RPCConnection connection : rpcConnections) {
            stop(connection.getHost(), connection.getPort());
        }
        //关闭线程组
        group.shutdownGracefully();
    }

    private void afterLogin(RPCConnection connection) {
        connectionMap.put(connection.getHostId(), connection);
        typeMap.computeIfAbsent(connection.getType(), k -> new ArrayList<>()).add(connection);

        connection.setRunning(true);
        if (moduleClientListener != null) {
            moduleClientListener.afterLogin(this, connection);
        }

        //通知服务器，主备连接都已准备妥当
        ClientConnectCompleteMessage msg = new ClientConnectCompleteMessage();
        msg.setActiveIndex(connection.getNextActiveIndex());
        msg.setModuleId(this.localHostId);
        connection.activeChannel().writeAndFlush(msg);
        log.info("模块客户端登录完毕, host:{}", connection);
    }

    /**
     * 链接已断开
     *
     * @param connection
     */
    public void serverUnavailable(RPCConnection connection) {
        if (!connection.isRunning()) {
            return;
        }
        connection.setRunning(false);
        if (moduleClientListener != null) {
            moduleClientListener.serverUnavailable(this, connection);
        }
    }

    /**
     * 销毁链接
     *
     * @param connection
     */
    public void serverDestroy(RPCConnection connection) {
        if (!connection.isRunning()) {
            return;
        }
        connection.setRunning(false);
        if (moduleClientListener != null) {
            moduleClientListener.serverDestroy(this, connection);
        }
    }

    /**
     * 连接服务器
     *
     * @param index channel数组的index
     * @return
     */
    public ChannelFuture connect(int index, RPCConnection connection) {
        if (connection.isDestroy()) {
            log.error("客户端已关闭[{}:{}/{}]", connection.getHost(), connection.getPort(), index);
            return null;
        }
        ChannelFuture f = bootstrap.connect(connection.getHost(), connection.getPort());
        f.addListener(new ChannelConnectListener(connection, index));
        return f;
    }


    public void login(RPCConnection connection, Channel channel, int index) {
        RegisterMessage register = new RegisterMessage();
        register.setChannelIndex(index);
        register.setHost(connection.getHost());
        register.setPort(connection.getPort());
        register.setModuleId(this.localHostId);
        register.setNoticeSign(this.noticeSign);

        log.info("注册模块channel,host:{}, index:{}", connection, connection.getActiveIndex());

        //注册channel
        channel.writeAndFlush(register);

        //启动心跳
        EXECUTOR.scheduleAtFixedRate(() -> {
            PingMessage ping = new PingMessage();
            channel.writeAndFlush(ping);
        }, 2000, 2000, TimeUnit.MILLISECONDS);
    }


    public void updateHeart(Session session) {
        ModuleSessionValue value = (ModuleSessionValue) session.getValue();
        value.setHeart(System.currentTimeMillis());
//        log.info("收到心跳：" + session.getChannel() + ",time:" + value.getHeart());
    }

    public void checkHeart() {
        if (isDebug){
            return;
        }
        long curTime = System.currentTimeMillis();
        for (RPCConnection connection : connectionMap.values()) {
            connection.checkHeart(curTime);
        }
    }

    public void registerComplete(RegisterMessage message) {
        String host = message.getHost();
        int port = message.getPort();
        int hostId = message.getModuleId();
        //找到我的connection
        RPCConnection connection = ipMap.get(host + ":" + port);
        if (connection == null) {
            //TODO 日志
            log.error("远程链接成功后，无法从ipMap中找到链接 host {} port {} ipMap内容 {}", host, port, JSON.toJSONString(ipMap));
            return;
        }

        //登录计数，两条连接都注册完毕才能算登录完毕
        connection.getLogin().incrementAndGet();

        connection.setHostId(hostId);
        afterLogin(connection);

        log.info("模块channel注册完毕, host:{}, index:{}", connection, connection.getActiveIndex());
    }


    public void serverDestroy(DestroyMessage msg) {
        int hostId = msg.getHostId();
        RPCConnection connection = connectionMap.get(hostId);
        if (connection == null) {
            return;
        }
        serverDestroy(connection);
    }

    /**
     * 是否可用
     *
     * @param hostId
     * @return
     */
    public boolean available(int hostId) {
        RPCConnection connection = connectionMap.get(hostId);
        if (connection == null) {
            return false;
        }
        return connection.available();
    }

    /**
     * 通过子类型获取连接列表
     *
     * @param childType
     * @return
     */
    public List<RPCConnection> getConnectionListByType(int childType) {
        List<RPCConnection> list = typeMap.getOrDefault(childType, Collections.emptyList());
        return list.stream().filter(RPCConnection::available).collect(Collectors.toList());
    }

    public Map<Integer, RPCConnection> getConnectionMap() {
        return connectionMap;
    }

    public void setConnectionMap(Map<Integer, RPCConnection> connectionMap) {
        this.connectionMap = connectionMap;
    }

    public RPCConnection getConnection(int hostId) {
        return connectionMap.get(hostId);
    }

    public int getLocalHostId() {
        return localHostId;
    }

    public void setLocalHostId(int localHostId) {
        this.localHostId = localHostId;
    }

    public void setModuleClientListener(ModuleClientListener moduleClientListener) {
        this.moduleClientListener = moduleClientListener;
    }

    public MessageTransformReceiver getMessageReceiver() {
        return messageReceiver;
    }

    public void setMessageReceiver(MessageTransformReceiver messageReceiver) {
        this.messageReceiver = messageReceiver;
    }

    public NoticeTransformReceiver getNoticeReceiver() {
        return noticeReceiver;
    }

    public void setNoticeReceiver(NoticeTransformReceiver noticeReceiver) {
        this.noticeReceiver = noticeReceiver;
    }

    public String getNoticeSign() {
        return noticeSign;
    }

    public void setNoticeSign(String noticeSign) {
        this.noticeSign = noticeSign;
    }

    public boolean isDebug() {
        return isDebug;
    }

    public void setDebug(boolean debug) {
        isDebug = debug;
    }
}
