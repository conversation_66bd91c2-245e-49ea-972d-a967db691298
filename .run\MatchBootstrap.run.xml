<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="MatchBootstrap" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.sh.game.server.pvp.match.PVPMatchBootstrap" />
    <module name="game-package-match" />
    <option name="PROGRAM_PARAMETERS" value="./conf/match.properties 1.0.0.1" />
    <option name="VM_PARAMETERS" value="
    -XX:+UseG1GC
    -Xms256m
    -Xmx512m
    -Dgame.log.queueLog=true
    -Dgame.log.MsgUnregister=true
    -Dlog4j.configurationFile=./release/docker/conf/match/log4j2-match.xml" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>