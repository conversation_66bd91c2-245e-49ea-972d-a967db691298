package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回人皇榜自己的面板信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRPCRhMyRankMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ResRPCRhMyRank proto;

    private com.sh.game.protos.RpcProtos.ResRPCRhMyRank.Builder builder;

	
	@Override
	public int getId() {
		return 82010;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ResRPCRhMyRank.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ResRPCRhMyRank.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ResRPCRhMyRank.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ResRPCRhMyRank getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ResRPCRhMyRank proto) {
        this.proto = proto;
    }

}
