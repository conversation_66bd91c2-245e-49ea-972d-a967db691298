package com.sh.game.notice;

import com.sh.commons.tuple.TwoTuple;
import lombok.extern.slf4j.Slf4j;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * notice中心，主要是保存notice发送相关的信息
 *
 * <AUTHOR>
 * @date 2020/8/18 17:43
 */
@Slf4j
public class NoticeCenter {


    private static ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor(r -> new Thread(r, "Notice超时检测线程"));

    private Map<Integer, TimeProcessNotice> timeNoticeMap = new ConcurrentHashMap<>();

    public NoticeCenter() {
        executor.scheduleAtFixedRate(new NoticeTimeOutChecker(this), 1000,
                1000, TimeUnit.MILLISECONDS);

    }

    public void addTimeNotice(TimeProcessNotice notice) {
        timeNoticeMap.put(notice.getNoticeIndex(), notice);
    }

    public void cleanTimeoutNotice() {

        long curTime = System.currentTimeMillis();
        Iterator<Map.Entry<Integer, TimeProcessNotice>> it = timeNoticeMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Integer, TimeProcessNotice> next = it.next();
            TimeProcessNotice notice = next.getValue();
            try {
                boolean timeout = notice.checkTimeout(curTime);

                if (timeout) {
                    it.remove();
                    log.error("移除超时的notice...");
                }
            } catch (Throwable throwable) {
                log.error("notice超时检查出错:" + notice.getClass().getName(), throwable);
            }
        }

    }

    public TwoTuple<Boolean, NoticeCallback> timeAndCallbackCheck(ProcessNotice notice) {


        if (notice.getNoticeIndex() == 0) {
            return new TwoTuple<>(true, null);
        }

        if (notice.first()) { //第一个
            return new TwoTuple<>(true, null);
        }

        TimeProcessNotice timeNotice = timeNoticeMap.remove(notice.getNoticeIndex());

        if (timeNotice == null) {
            log.info("time notice返回的时候已超时：{}", notice.getClass().getName());
            return new TwoTuple<>(false, null);
        }

        //如果有回调的，需要构建callback
        NoticeCallback callback = timeNotice.getCallback();
        if (callback != null) {
            callback.setNotice(notice);
        }
        return new TwoTuple<>(true, callback);


    }


}
