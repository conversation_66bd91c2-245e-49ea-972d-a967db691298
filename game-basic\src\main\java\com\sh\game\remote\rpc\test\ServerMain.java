package com.sh.game.remote.rpc.test;

import com.sh.game.remote.rpc.MessageTransformReceiver;
import com.sh.game.remote.rpc.NoticeTransformReceiver;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.server.ModuleNet;
import com.sh.game.remote.rpc.server.ModuleServerListener;

/**
 * ServerMain
 *
 * <AUTHOR>
 * @date 2020/8/27 16:57
 */
public class ServerMain {

    public static void main(String[] args) {

        ModuleNet net = new ModuleNet();
        net.setModuleServerListener(new ModuleServerListener() {
            @Override
            public void clientUnavailable(RPCConnection connection) {

            }

            @Override
            public void clientDestroy(RPCConnection connection) {

            }

            @Override
            public void clientConnectComplete(RPCConnection connection) {

            }
        });

        net.setMessageReceiver(new MessageTransformReceiver() {

            @Override
            public void receive(MessageTransform msg) {
                System.out.println("收到 msg transform");
                RPCConnection client = net.getClient(1);
                client.activeChannel().writeAndFlush(msg);
            }
        });

        net.setNoticeReceiver(new NoticeTransformReceiver() {
            @Override
            public void receive(NoticeTransform noticeTransform) {
                System.out.println("收到 notice transform");
                RPCConnection client = net.getClient(1);
                client.activeChannel().writeAndFlush(noticeTransform);
            }
        });
        net.setHostId(203);
        net.start(20001);
    }
}
