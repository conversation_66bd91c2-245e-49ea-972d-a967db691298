# 技术栈

## 系统架构
- **架构模式**: 分布式微服务架构，支持多服务器横向扩展
- **通信协议**: 基于WebSocket的长连接通信，支持实时数据传输
- **数据同步**: 跨服数据同步和状态管理机制
- **负载均衡**: 支持多实例部署和流量分发

## 后端技术栈

### 核心框架
- **Java版本**: OpenJDK 1.8
- **构建工具**: Apache Maven 3.x
- **网络框架**: Netty 4.1.94.Final (高性能异步网络通信)
- **序列化**: Protocol Buffers (protobuf) + FastJSON 1.2.31
- **依赖注入**: 自定义轻量级IOC容器

### 数据存储
- **关系数据库**: MySQL 8.0.21
- **缓存数据库**: Redis (用于会话管理和热点数据缓存)
- **数据访问**: 自研ORM框架，支持实体类自动映射

### 基础组件
- **日志系统**: SLF4J + Log4j2 2.22.0
- **HTTP客户端**: Apache HttpComponents 4.5.3
- **工具库**: Google Guava 19.0, Apache Commons Codec
- **代码生成**: CGLib 3.3.0 动态代理
- **开发工具**: Lombok 1.18.30 简化Java代码

## 开发环境配置

### 必需工具
- **JDK**: Java 8 或更高版本
- **Maven**: 3.6+ 用于项目构建和依赖管理
- **IDE推荐**: IntelliJ IDEA (支持Java和Maven)
- **数据库**: MySQL 8.0+ 和 Redis 6.0+

### 常用开发命令
```bash
# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install
```

## 环境变量配置

### 关键配置文件
- `conf/game.properties`: 游戏服务器核心配置
- `conf/db.properties`: 数据库连接配置  
- `conf/redis.properties`: Redis连接配置
- `conf/scene.properties`: 场景服务器配置
- `conf/log4j2.xml`: 日志配置

### 重要环境变量
- `serverId`: 服务器唯一标识
- `platformId`: 平台ID
- `gameServerPort`: 游戏服务端口 (默认: 12010)
- `httpServerPort`: HTTP服务端口 (默认: 9092)
- `backServerPort`: 后台管理端口 (默认: 9091)
- `configDataPath`: 配置数据文件路径
- `debug`: 调试模式开关

## 端口配置

### 标准端口分配
- **游戏服务**: 12010 (WebSocket连接)
- **HTTP服务**: 9092 (RESTful API)
- **后台管理**: 9091 (管理面板)
- **跨服通信**: 9200 (服务器间通信)
- **匹配服务**: 9400 (PvP匹配)

## 部署架构

### 服务类型
- **游戏服 (serverType=1)**: 主要游戏逻辑处理
- **跨服 (serverType=2)**: 跨服活动和数据同步
- **战斗服 (serverType=3)**: 专门处理PvP战斗
- **匹配服 (serverType=4)**: PvP匹配和组队

### 外部依赖
- **内部Maven仓库**: http://192.168.5.12:8081/nexus/
- **阿里云Maven**: http://maven.aliyun.com/nexus/content/groups/public/
- **中心服务API**: 用于礼包卡和全局排行榜数据交互

## 自研框架特性

### 数据持久化框架
- **实体映射**: 通过@Tag注解自动序列化
- **DAO模板**: PersistDaoTemplate支持自定义数据访问层
- **通用操作**: DataCenter.get()/update() 简化数据操作
- **定时持久**: @PersistPeriod 可配置数据保存间隔

### 消息处理框架
- **协议自动生成**: 基于protobuf的消息协议
- **消息路由**: CommandRouter自动分发消息到处理器
- **连接管理**: 支持多连接池和连接状态监控
- **限流保护**: 单协议号每秒请求次数限制

### 事件系统
- **事件驱动**: 基于观察者模式的游戏事件处理
- **动态监听**: IDynamicListener支持运行时注册事件
- **异步通知**: NoticeCenter异步事件通知机制

## 性能优化

### 内存管理
- **对象池**: 重用频繁创建的对象减少GC压力
- **缓存策略**: 多级缓存提升数据访问性能
- **连接池**: 数据库和Redis连接池优化

### 并发处理
- **异步IO**: Netty事件驱动模型处理大量并发连接
- **线程池**: 分离网络IO和业务逻辑处理线程
- **无锁设计**: 尽量避免锁竞争的数据结构设计