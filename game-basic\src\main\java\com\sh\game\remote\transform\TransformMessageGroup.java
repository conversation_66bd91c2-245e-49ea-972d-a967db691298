package com.sh.game.remote.transform;

import com.sh.net.Message;

/**
 * 转发group Message给玩家，该消息仅仅是用来转发一个byte数组，自身一点逻辑没有
 */
public class TransformMessageGroup implements Message {


	private int length = 0;

	byte[] bytes;

	public TransformMessageGroup(byte[] bytes) {
		this.bytes = bytes;
	}


	@Override
	public byte[] encode() {

		if (bytes == null || bytes.length == 0) {
			return null;
		}
		return bytes;
	}



	@Override
	public void decode(byte[] bytes) {

	}

	@Override
	public int length() {
		return this.length;
	}

	@Override
	public void setLength(int length) {
		this.length = length;
	}

	@Override
	public int getId() {
		return -1;
	}



	@Override
	public void setSequence(short sequence) {

	}

	@Override
	public short getSequence() {
		return 0;
	}

}
