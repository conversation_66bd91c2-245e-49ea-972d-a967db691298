package com.sh.game.server;

import com.sh.concurrent.IQueueDriverCommand;
import com.sh.game.notice.ProcessNotice;
import com.sh.server.AbstractHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 命令路由
 * <AUTHOR>
 * @date 2018/12/14 17:39
 */
@Slf4j
public abstract class CommandRouter {

    protected Map<Integer, CommandProcessor> processors = new HashMap<>();

    public CommandRouter() {
        register();
    }

    public abstract void register();

    public void registerProcessor(int processorId, CommandProcessor consumer) {
        processors.put(processorId, consumer);
    }

    public CommandProcessor getProcessor(int processorId) {
        return processors.get(processorId);
    }

    public Map<Integer, CommandProcessor> getProcessors() {
        return processors;
    }

    public boolean hasProcessor(int processorId) {
        return processors.containsKey(processorId);
    }


    public void process(int processorId, AbstractHandler handler) {
        CommandProcessor processor = processors.get(processorId);
        if (processor == null) {
            log.error("找不到Processor:{}",processorId);
            return;
        }
        processor.process(handler);
        //1. 如果是地图模块，那么其实应该找到地图模块

    }

    public void process(int processorId, IQueueDriverCommand command, long queueKey) {
        CommandProcessor processor = processors.get(processorId);
        if (processor == null) {
            log.error("找`不到Processor:{}",processorId);
        }
        processor.process(command, queueKey);
    }

    public void process(int processorId, ProcessNotice notice, long queueKey) {
        CommandProcessor processor = processors.get(processorId);
        if (processor == null) {
            log.error("找不到Processor:{},notice:{},commandRouter:{}", processorId, notice.getClass().getSimpleName(), this.getClass().getSimpleName());
        }
        processor.process(notice, queueKey);
    }

}
