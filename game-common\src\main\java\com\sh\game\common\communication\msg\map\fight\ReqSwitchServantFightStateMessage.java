package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求切换宠物状态</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toScene")
public class ReqSwitchServantFightStateMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ReqSwitchServantFightState proto;

    private com.sh.game.protos.FightProtos.ReqSwitchServantFightState.Builder builder;

	
	@Override
	public int getId() {
		return 69018;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ReqSwitchServantFightState.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ReqSwitchServantFightState.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ReqSwitchServantFightState.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ReqSwitchServantFightState getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ReqSwitchServantFightState proto) {
        this.proto = proto;
    }

}
