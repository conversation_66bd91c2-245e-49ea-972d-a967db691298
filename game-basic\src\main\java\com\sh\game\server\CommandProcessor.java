package com.sh.game.server;

import com.sh.concurrent.IQueueDriverCommand;
import com.sh.concurrent.QueueDriver;
import com.sh.game.notice.NoticeCommand;
import com.sh.game.notice.NoticeHandler;
import com.sh.game.notice.NoticePool;
import com.sh.game.notice.ProcessNotice;
import com.sh.server.AbstractHandler;
import com.sh.server.AbstractMessage;
import com.sh.server.Session;
import lombok.extern.slf4j.Slf4j;

/**
 * 命令处理器
 * <AUTHOR>
 * @date 2018/12/14 18:54
 */
@Slf4j
public abstract class CommandProcessor {

    public abstract byte id();

    protected abstract QueueDriver getDriver(long key);

    protected abstract NoticePool getNoticePool();

    protected boolean isSessionLegal(Session session) {
        return true;
    }

    public void process(IQueueDriverCommand command, long key) {
        QueueDriver driver = getDriver(key);
        if (driver == null) {
            log.error("process failed. driver not available: {} - {}", this.getClass().getName(), key);
            if (command instanceof NoticeCommand) {
                log.error("process failed. command method  {}",((NoticeCommand)command).getMethod());
            }
            return;
        }

        driver.addCommand(command);
    }

    public void process(ProcessNotice notice, long key) {
        NoticeHandler handler = getNoticePool().getHandler(notice.getNoticeID());
        if (handler == null) {
            log.error("process failed. notice handler not available: {} - {}", this.getClass().getName(), notice.getClass().getName());
            return;
        }

        NoticeCommand command = new NoticeCommand(handler.getAction(), handler.getMethod(), notice);
        process(command, key);
    }

    public void process(AbstractHandler handler) {
        AbstractMessage message = (AbstractMessage) handler.getMsg();
        Session session = message.getSession();
        if (session == null) {
            log.error("process failed. session not exist: {} - {}", this.getClass().getName(), message.getClass().getName());
            return;
        }
        if (!isSessionLegal(session)) {
            log.error("process failed. session illegal: {} - {}", this.getClass().getName(), session);
            return;
        }

        process(handler, session.getId());
    }
}
