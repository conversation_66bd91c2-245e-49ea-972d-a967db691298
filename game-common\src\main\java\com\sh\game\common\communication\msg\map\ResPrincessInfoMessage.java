package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.map.bean.PrincessBean;
import com.sh.game.common.communication.msg.map.bean.UnionInfoBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回公主信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResPrincessInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 193003;
	}
	
	/**
	 * 公主信息
	 */
	private List<PrincessBean> beans = new ArrayList<>();
	/**
	 * 行会排名信息
	 */
	private List<UnionInfoBean> unionBeans = new ArrayList<>();

	public List<PrincessBean> getBeans() {
		return beans;
	}

	public void setBeans(List<PrincessBean> beans) {
		this.beans = beans;
	}
	public List<UnionInfoBean> getUnionBeans() {
		return unionBeans;
	}

	public void setUnionBeans(List<UnionInfoBean> unionBeans) {
		this.unionBeans = unionBeans;
	}

	@Override
	public boolean read(KryoInput buf) {

		int beansLength = readShort(buf);
		for (int beansI = 0; beansI < beansLength; beansI++) {
			if (readByte(buf) == 0) { 
				this.beans.add(null);
			} else {
				PrincessBean princessBean = new PrincessBean();
				princessBean.read(buf);
				this.beans.add(princessBean);
			}
		}
		int unionBeansLength = readShort(buf);
		for (int unionBeansI = 0; unionBeansI < unionBeansLength; unionBeansI++) {
			if (readByte(buf) == 0) { 
				this.unionBeans.add(null);
			} else {
				UnionInfoBean unionInfoBean = new UnionInfoBean();
				unionInfoBean.read(buf);
				this.unionBeans.add(unionInfoBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.beans.size());
		for (int beansI = 0; beansI < this.beans.size(); beansI++) {
			this.writeBean(buf, this.beans.get(beansI));
		}
		writeShort(buf, this.unionBeans.size());
		for (int unionBeansI = 0; unionBeansI < this.unionBeans.size(); unionBeansI++) {
			this.writeBean(buf, this.unionBeans.get(unionBeansI));
		}
		return true;
	}
}
