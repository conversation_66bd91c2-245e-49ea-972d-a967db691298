package com.sh.game.common.communication.msg.map.duplicate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>发送伤害排行变化信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResWorldBossHurtRankInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo proto;

    private com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo.Builder builder;

	
	@Override
	public int getId() {
		return 71010;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DuplicateProtos.ResWorldBossHurtRankInfo proto) {
        this.proto = proto;
    }

}
