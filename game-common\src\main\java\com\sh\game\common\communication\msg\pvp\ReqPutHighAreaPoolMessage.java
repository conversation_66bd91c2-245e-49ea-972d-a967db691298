package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.MatchPlayerBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>往高级赛区池塞玩家</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqPutHighAreaPoolMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125024;
	}
	
	/**
	 * 玩家信息
	 */
	private MatchPlayerBean bean;
	/**
	 * 洗牌时间戳
	 */
	private int time;
	/**
	 * 数据中心服清空数据
	 */
	private int clearTime;
	/**
	 * 冒泡赛结束时间,需要选取16强进行淘汰赛
	 */
	private int bubbleMatchEnd;
	/**
	 * 赛区
	 */
	private List<Integer> areas = new ArrayList<>();
	/**
	 * 系统匹配组
	 */
	private int matchGroup;
	/**
	 * 赛季
	 */
	private int season;

	public MatchPlayerBean getBean() {
		return bean;
	}

	public void setBean(MatchPlayerBean bean) {
		this.bean = bean;
	}

		public int getTime() {
		return time;
	}

	public void setTime(int time) {
		this.time = time;
	}

		public int getClearTime() {
		return clearTime;
	}

	public void setClearTime(int clearTime) {
		this.clearTime = clearTime;
	}

		public int getBubbleMatchEnd() {
		return bubbleMatchEnd;
	}

	public void setBubbleMatchEnd(int bubbleMatchEnd) {
		this.bubbleMatchEnd = bubbleMatchEnd;
	}

		public List<Integer> getAreas() {
		return areas;
	}

	public void setAreas(List<Integer> areas) {
		this.areas = areas;
	}
	public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

		public int getSeason() {
		return season;
	}

	public void setSeason(int season) {
		this.season = season;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		if (readByte(buf) != 0) {
			MatchPlayerBean matchPlayerBean = new MatchPlayerBean();
			matchPlayerBean.read(buf);
			this.bean = matchPlayerBean;
		}
		this.time = readInt(buf, false);
		this.clearTime = readInt(buf, false);
		this.bubbleMatchEnd = readInt(buf, false);
		int areasLength = readShort(buf);
		for (int areasI = 0; areasI < areasLength; areasI++) {
			this.areas.add(this.readInt(buf, false));
		}
		this.matchGroup = readInt(buf, false);
		this.season = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeBean(buf, bean);
		this.writeInt(buf, time, false);
		this.writeInt(buf, clearTime, false);
		this.writeInt(buf, bubbleMatchEnd, false);
		writeShort(buf, this.areas.size());
		for (int areasI = 0; areasI < this.areas.size(); areasI++) {
			this.writeInt(buf, this.areas.get(areasI), false);
		}
		this.writeInt(buf, matchGroup, false);
		this.writeInt(buf, season, false);
		return true;
	}
}
