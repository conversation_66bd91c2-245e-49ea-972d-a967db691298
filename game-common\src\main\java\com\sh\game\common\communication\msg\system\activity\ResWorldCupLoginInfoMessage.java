package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回签到信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResWorldCupLoginInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo proto;

    private com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo.Builder builder;

	
	@Override
	public int getId() {
		return 4432;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ActivityProtos.ResWorldCupLoginInfo proto) {
        this.proto = proto;
    }

}
