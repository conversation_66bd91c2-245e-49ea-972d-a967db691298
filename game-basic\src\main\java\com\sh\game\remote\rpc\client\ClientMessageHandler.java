package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.msg.DestroyMessage;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.server.AbstractHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * ServerMessageHandler
 *
 * <AUTHOR>
 * @date 2020/8/27 13:21
 */
@Slf4j
class ClientMessageHandler {

    private ModuleClient moduleClient;

    public ClientMessageHandler(ModuleClient moduleClient) {
        this.moduleClient = moduleClient;
    }

    //这个需要调用我提供的ModuleClient的方法
    //当有多个ModuleClient的时候，怎么保证这些handler调用的是不同的ModuleClient
    class RegisterHandler extends AbstractHandler<RegisterMessage> {

        @Override
        public void doAction(RegisterMessage msg) {
            if (msg.isRegisterFail()) {
                log.error("注册跨服时moduleId重复，注册失败，moduleId = {}",msg.getModuleId());
                return;
            }
            moduleClient.registerComplete(msg);
        }
    }

    class PingHandler extends AbstractHandler<PingMessage> {

        @Override
        public void doAction(PingMessage msg) {
            moduleClient.updateHeart(msg.getSession());
        }
    }


    class DestroyHandler extends AbstractHandler<DestroyMessage> {

        @Override
        public void doAction(DestroyMessage msg) {
            moduleClient.serverDestroy(msg);
        }
    }


    class MessageTransformHandler extends AbstractHandler<MessageTransform> {

        @Override
        public void doAction(MessageTransform msg) {
            //需要一个规则来执行Message
            moduleClient.getMessageReceiver().receive(msg);
        }
    }

    class NoticeTransformHandler extends AbstractHandler<NoticeTransform> {

        @Override
        public void doAction(NoticeTransform msg) {
            //需要规则来执行Notice
            moduleClient.getNoticeReceiver().receive(msg);
        }
    }

}
