package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回释放技能造成的位移消息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResCastSkillPosChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ResCastSkillPosChange proto;

    private com.sh.game.protos.FightProtos.ResCastSkillPosChange.Builder builder;

	
	@Override
	public int getId() {
		return 69014;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ResCastSkillPosChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ResCastSkillPosChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ResCastSkillPosChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ResCastSkillPosChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ResCastSkillPosChange proto) {
        this.proto = proto;
    }

}
