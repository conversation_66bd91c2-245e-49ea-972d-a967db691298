package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ItemImmobilization extends KryoBean {

	/**
	 * 产生行为
	 */
	private int action;
	/**
	 * 过期时间 时间戳
	 */
	private int expire;

	public int getAction() {
		return action;
	}

	public void setAction(int action) {
		this.action = action;
	}

		public int getExpire() {
		return expire;
	}

	public void setExpire(int expire) {
		this.expire = expire;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.action = readInt(buf, false);
		this.expire = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, action, false);
		this.writeInt(buf, expire, false);
		return true;
	}
}
