package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class BattlePlayerBean extends KryoBean {

	/**
	 * 玩家id
	 */
	private long rid;
	/**
	 * 玩家昵称
	 */
	private String roleName;
	/**
	 * 特殊参数
	 */
	private String param;
	/**
	 * 玩家所在服务名称
	 */
	private String serverName;

	private int hostId;

	public long getRid() {
		return rid;
	}

	public void setRid(long rid) {
		this.rid = rid;
	}

		public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

		public String getParam() {
		return param;
	}

	public void setParam(String param) {
		this.param = param;
	}

		public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.rid = readLong(buf);
		this.roleName = readString(buf);
		this.param = readString(buf);
		this.serverName = readString(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, rid);
		this.writeString(buf, roleName);
		this.writeString(buf, param);
		this.writeString(buf, serverName);
		return true;
	}
}
