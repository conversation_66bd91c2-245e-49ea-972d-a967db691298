package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求英雄释放合体技</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toScene")
public class ReqUseHeroCombSkillMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ReqUseHeroCombSkill proto;

    private com.sh.game.protos.FightProtos.ReqUseHeroCombSkill.Builder builder;

	
	@Override
	public int getId() {
		return 69024;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ReqUseHeroCombSkill.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ReqUseHeroCombSkill.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ReqUseHeroCombSkill.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ReqUseHeroCombSkill getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ReqUseHeroCombSkill proto) {
        this.proto = proto;
    }

}
