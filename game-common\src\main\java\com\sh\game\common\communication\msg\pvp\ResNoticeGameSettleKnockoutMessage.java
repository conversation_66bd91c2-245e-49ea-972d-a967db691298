package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>匹配服通知游戏服结算赌池</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResNoticeGameSettleKnockoutMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125029;
	}
	
	/**
	 * pvpGroup
	 */
	private int pvpGroup;
	/**
	 * 几强
	 */
	private int number;
	/**
	 * 位置
	 */
	private int index;

	public int getPvpGroup() {
		return pvpGroup;
	}

	public void setPvpGroup(int pvpGroup) {
		this.pvpGroup = pvpGroup;
	}

		public int getNumber() {
		return number;
	}

	public void setNumber(int number) {
		this.number = number;
	}

		public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.pvpGroup = readInt(buf, false);
		this.number = readInt(buf, false);
		this.index = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, pvpGroup, false);
		this.writeInt(buf, number, false);
		this.writeInt(buf, index, false);
		return true;
	}
}
