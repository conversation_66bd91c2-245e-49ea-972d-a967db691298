package com.sh.game.remote;

import com.sh.client.Client;
import com.sh.client.ClientBuilder;
import com.sh.client.ClientListener;
import com.sh.game.notice.ProcessNotice;
import com.sh.game.server.AbstractHandlerPool;
import com.sh.game.server.CommandRouter;
import com.sh.game.server.MessageConsumer;
import com.sh.net.Message;
import com.sh.net.MessagePool;
import com.sh.net.NetworkEventlistener;
import io.netty.channel.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.Collection;

/**
 * 远程主机
 * <AUTHOR>
 * @date 2018/8/30 20:29
 */
@Setter
@Getter
@Slf4j
public abstract class RemoteHost {




    /**
     * 远程主机id
     */
    private int id;

    /**
     * ip
     */
    private String host;

    /**
     * 端口
     */
    private int port;

    /**
     * 远程主机客户端
     */
    protected Client client;

    /**
     * 是否已登录
     */
    private boolean login;



    /**
     * 开服时间
     */
    private long openTime;

    /**
     * 合服时间
     */
    private long combineTime;

    /**
     * 命令路由
     */
    private CommandRouter router;

    /**
     * 事件监听器
     */
    private NetworkEventlistener eventlistener;

    /**
     * 远程服务器类型（用于游戏服）
     */
    private int remoteType;

    private MessagePool msgPool;

    private AbstractHandlerPool handlerPool;

    private ClientListener clientListener;

    /**
     * 空置时间
     */
    private long emptyTime;




    public RemoteHost() {

    }


    /**
     * 获取开服天数,开服首日算作第一天
     *
     * @return
     */
    public int getOpenServerDay() {
        LocalDateTime ldt = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.openTime), ZoneId.systemDefault());
        return (int) (LocalDateTime.now().getLong(ChronoField.EPOCH_DAY) - ldt.getLong(ChronoField.EPOCH_DAY) + 1);
    }


    /**
     * 获取合服天数，合服首日算作第一天
     *
     * @return
     */
    public int getCombineServerDay() {
        if (this.combineTime == 0L) {
            return 0;
        }
        LocalDateTime ldt = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.combineTime), ZoneId.systemDefault());
        return (int) (LocalDateTime.now().getLong(ChronoField.EPOCH_DAY) - ldt.getLong(ChronoField.EPOCH_DAY) + 1);
    }

    /**
     * 连接
     */
    public void connect() {
        try {

            /**
             * 此处不用client底层的心跳检测机制，使用外部逻辑检查，实现更完善的判断
             */

            ClientBuilder builder = new ClientBuilder();

            builder.setHost(host);
            builder.setPort(port);
            builder.setEventlistener(eventlistener);

            builder.setConsumer(new MessageConsumer(router, handlerPool));

            builder.setMsgPool(msgPool);

            builder.setClientListener(clientListener);

            builder.setNeedReconnect(true);
            builder.setPooled(false);

            client = builder.createClient();
            client.connect(true);

        } catch (Exception e) {
            log.error("连接远程服务器失败："+this.host+":"+this.port);
        }
    }


    public void registerChannel(Channel channel) {
        this.client.registerChannel(0, channel);
    }

    public void unregisterChannel(int index) {
        this.client.unregisterChannel(index);
    }

    //转发给地图
    public abstract void transformToMap(Message message, long pid);
    public abstract void transformToPlayer(Message message, long id);
    public abstract void transformToPlayer(Message message, Collection<Long> idList);
    public abstract void transformToWorld(Message message, int localId);
    public abstract void transformNotice(ProcessNotice notice, byte processId, long playerId);
    public abstract void transformToAllHost(Message message, int localId);
}
