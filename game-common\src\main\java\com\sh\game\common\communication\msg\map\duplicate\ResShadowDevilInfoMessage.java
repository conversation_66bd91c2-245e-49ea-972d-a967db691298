package com.sh.game.common.communication.msg.map.duplicate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回影魔王副本信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResShadowDevilInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo proto;

    private com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo.Builder builder;

	
	@Override
	public int getId() {
		return 71007;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DuplicateProtos.ResShadowDevilInfo proto) {
        this.proto = proto;
    }

}
