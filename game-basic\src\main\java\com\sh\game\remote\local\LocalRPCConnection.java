package com.sh.game.remote.local;

import com.sh.game.notice.ProcessNotice;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.net.Message;
import com.sh.net.NetworkConsumer;
import com.sh.server.Session;
import io.netty.channel.Channel;

import java.util.Collection;

/**
 * LocalRPCConnection
 *
 * <AUTHOR>
 * @date 2020/9/7 16:07
 */
public class LocalRPCConnection extends RPCConnection {

    private NetworkConsumer peerConsumer;

    private LocalChannel peerChannel;

    public LocalRPCConnection() {
        this.peerChannel = new LocalChannel();
        //设置session
        Session session = new Session();
        session.setChannel(this.peerChannel);
        AttributeUtil.set(this.peerChannel, ChannelAttrKey.SESSION, session);
    }


    public boolean checkHeart(long curTime) {
        return true;
    }

    public void transformMessage(Message message, long id) {
        MessageTransform transform = new MessageTransform();
        transform.getPidList().add(id);
        transform.setMessageId(message.getId());
        transform.setMessage(message);
        transform.setEncode(false);
        //没有channel
        // 此地直接交付消息
        peerConsumer.consume(peerChannel, transform);
    }
    public void transformMessage(Message message, Collection<Long> idList) {
        MessageTransform transform = new MessageTransform();
        transform.getPidList().addAll(idList);
        transform.setMessageId(message.getId());
        transform.setMessage(message);
        transform.setEncode(false);
        //没有channel
        // 此地直接交付消息
        peerConsumer.consume(peerChannel, transform);
    }

    public void transformNotice(ProcessNotice notice, int processorId, long id) {
        NoticeTransform transform = new NoticeTransform();
        transform.setNoticeId(notice.getNoticeID());
        transform.setNotice(notice);
        transform.setPlayerId(id);
        transform.setProcessId((byte) processorId);
        transform.setEncode(false);

        //没有channel
        // 此地直接交付消息
        peerConsumer.consume(peerChannel, transform);
    }

    @Override
    public Channel activeChannel() {
        return this.peerChannel;
    }

    public void setPeerConsumer(NetworkConsumer peerConsumer) {
        this.peerConsumer = peerConsumer;
        this.peerChannel.setConsumer(peerConsumer);
    }

    public Channel getPeerChannel() {
        return peerChannel;
    }


    public boolean available() {
        return true;
    }
}
