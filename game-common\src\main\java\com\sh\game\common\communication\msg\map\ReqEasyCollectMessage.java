package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求简易采集信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqEasyCollectMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ReqEasyCollect proto;

    private com.sh.game.protos.MapProtos.ReqEasyCollect.Builder builder;

	
	@Override
	public int getId() {
		return 67090;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ReqEasyCollect.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ReqEasyCollect.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ReqEasyCollect.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ReqEasyCollect getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ReqEasyCollect proto) {
        this.proto = proto;
    }

}
