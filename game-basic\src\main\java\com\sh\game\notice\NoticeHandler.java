package com.sh.game.notice;

import java.lang.reflect.Method;

/**
 * notice的处理器，保存notice执行的对象以及方法
 * <AUTHOR>
 * @date 2020/8/18 13:06
 */
public class NoticeHandler {

    private final Object action;

    private final Method method;

    public NoticeHandler(Object handler, Method method) {
        this.action = handler;
        this.method = method;
    }

    public Object getAction() {
        return action;
    }

    public Method getMethod() {
        return method;
    }


}
