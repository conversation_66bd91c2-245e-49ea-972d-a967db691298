package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.MatchResultBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>群雄逐鹿 匹配结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPvpSBKMatchResultMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125023;
	}
	
	/**
	 * 帮会id
	 */
	private long unionId;
	/**
	 * 匹配结果列表
	 */
	private List<MatchResultBean> matchResultBean = new ArrayList<>();

	public long getUnionId() {
		return unionId;
	}

	public void setUnionId(long unionId) {
		this.unionId = unionId;
	}

		public List<MatchResultBean> getMatchResultBean() {
		return matchResultBean;
	}

	public void setMatchResultBean(List<MatchResultBean> matchResultBean) {
		this.matchResultBean = matchResultBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.unionId = readLong(buf);
		int matchResultBeanLength = readShort(buf);
		for (int matchResultBeanI = 0; matchResultBeanI < matchResultBeanLength; matchResultBeanI++) {
			if (readByte(buf) == 0) { 
				this.matchResultBean.add(null);
			} else {
				MatchResultBean matchResultBean = new MatchResultBean();
				matchResultBean.read(buf);
				this.matchResultBean.add(matchResultBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, unionId);
		writeShort(buf, this.matchResultBean.size());
		for (int matchResultBeanI = 0; matchResultBeanI < this.matchResultBean.size(); matchResultBeanI++) {
			this.writeBean(buf, this.matchResultBean.get(matchResultBeanI));
		}
		return true;
	}
}
