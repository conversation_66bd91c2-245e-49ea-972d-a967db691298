package com.sh.game.server;

import com.sh.game.notice.ProcessNotice;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.server.AbstractMessage;

import java.util.List;

public abstract class GameModule {

    protected ModuleClient client;

    public ModuleClient getClient() {
        return client;
    }

    public void setClient(ModuleClient client) {
        this.client = client;
    }

    public abstract int getModuleId();
    public abstract List<Integer> processorIdList();
    public abstract void receive(AbstractMessage message);
    public abstract void receive(int processorId, ProcessNotice notice, long queueKey);
}
