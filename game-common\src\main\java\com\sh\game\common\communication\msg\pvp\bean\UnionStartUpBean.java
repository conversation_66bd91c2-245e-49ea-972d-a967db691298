package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class UnionStartUpBean extends KryoBean {

	/**
	 * 帮会id
	 */
	private long uId;
	/**
	 * 帮主名称
	 */
	private String guildLeaderName;
	/**
	 * 帮会名称
	 */
	private String UnionName;
	/**
	 * 帮会所在服务器id
	 */
	private int hostId;
	/**
	 * 帮派前10名玩家总战力
	 */
	private long totalPower;
	/**
	 * 帮会积分 匹配时先按积分排序 积分一样用战力替换位置
	 */
	private int unionScore;
	/**
	 * 帮会等级
	 */
	private int level;
	/**
	 * 帮主id
	 */
	private long leaderId;

	public long getUId() {
		return uId;
	}

	public void setUId(long uId) {
		this.uId = uId;
	}

		public String getGuildLeaderName() {
		return guildLeaderName;
	}

	public void setGuildLeaderName(String guildLeaderName) {
		this.guildLeaderName = guildLeaderName;
	}

		public String getUnionName() {
		return UnionName;
	}

	public void setUnionName(String UnionName) {
		this.UnionName = UnionName;
	}

		public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public long getTotalPower() {
		return totalPower;
	}

	public void setTotalPower(long totalPower) {
		this.totalPower = totalPower;
	}

		public int getUnionScore() {
		return unionScore;
	}

	public void setUnionScore(int unionScore) {
		this.unionScore = unionScore;
	}

		public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

		public long getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(long leaderId) {
		this.leaderId = leaderId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.uId = readLong(buf);
		this.guildLeaderName = readString(buf);
		this.UnionName = readString(buf);
		this.hostId = readInt(buf, false);
		this.totalPower = readLong(buf);
		this.unionScore = readInt(buf, false);
		this.level = readInt(buf, false);
		this.leaderId = readLong(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, uId);
		this.writeString(buf, guildLeaderName);
		this.writeString(buf, UnionName);
		this.writeInt(buf, hostId, false);
		this.writeLong(buf, totalPower);
		this.writeInt(buf, unionScore, false);
		this.writeInt(buf, level, false);
		this.writeLong(buf, leaderId);
		return true;
	}
}
