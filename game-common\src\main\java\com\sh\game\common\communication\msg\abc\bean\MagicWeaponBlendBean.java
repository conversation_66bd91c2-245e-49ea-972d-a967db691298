package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class MagicWeaponBlendBean extends KryoBean {

	/**
	 * 孔位配置id
	 */
	private int slotConfigId;
	/**
	 * 法宝配置id
	 */
	private int itemConfigId;
	/**
	 * 法宝属性万分比
	 */
	private int magicWeaponRate;
	/**
	 * 法宝携带的buffId
	 */
	private int magicWeaponBuffId;

	public int getSlotConfigId() {
		return slotConfigId;
	}

	public void setSlotConfigId(int slotConfigId) {
		this.slotConfigId = slotConfigId;
	}

	public int getItemConfigId() {
		return itemConfigId;
	}

	public void setItemConfigId(int itemConfigId) {
		this.itemConfigId = itemConfigId;
	}

	public int getMagicWeaponRate() {
		return magicWeaponRate;
	}

	public void setMagicWeaponRate(int magicWeaponRate) {
		this.magicWeaponRate = magicWeaponRate;
	}

	public int getMagicWeaponBuffId() {
		return magicWeaponBuffId;
	}

	public void setMagicWeaponBuffId(int magicWeaponBuffId) {
		this.magicWeaponBuffId = magicWeaponBuffId;
	}


	@Override
	public boolean read(KryoInput buf) {

		this.slotConfigId = readInt(buf, false);
		this.itemConfigId = readInt(buf, false);
		this.magicWeaponRate = readInt(buf, false);
		this.magicWeaponBuffId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, slotConfigId, false);
		this.writeInt(buf, itemConfigId, false);
		this.writeInt(buf, magicWeaponRate, false);
		this.writeInt(buf, magicWeaponBuffId, false);
		return true;
	}
}
