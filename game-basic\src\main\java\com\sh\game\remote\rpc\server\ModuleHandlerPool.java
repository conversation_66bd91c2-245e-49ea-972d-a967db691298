package com.sh.game.remote.rpc.server;


import com.sh.game.remote.rpc.msg.ClientConnectCompleteMessage;
import com.sh.game.remote.rpc.msg.DestroyMessage;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.game.server.AbstractHandlerPool;
import com.sh.server.AbstractHandler;
import com.sh.server.AbstractMessage;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/8/30 10:21
 */
@Slf4j
public class ModuleHandlerPool extends AbstractHandlerPool {

    private ServerMessageHandler serverMessageHandler;

    private Map<Integer, Constructor<? extends AbstractHandler<? extends AbstractMessage>>>
            constructorMap = new HashMap<>();


    public ModuleHandlerPool(ServerMessageHandler clientMessageHandler) {

        this.serverMessageHandler = clientMessageHandler;

        //notice传输
        register(new NoticeTransform(), ServerMessageHandler.NoticeTransformHandler.class, (byte)21);

        //message传输
        register(new MessageTransform(), ServerMessageHandler.MessageTransformHandler.class, (byte)21);

        //心跳
        register(new PingMessage(), ServerMessageHandler.PingHandler.class, (byte)22);

        //请求注册
        register(new RegisterMessage(),ServerMessageHandler.RegisterHandler.class, (byte)22);

        //对端销毁
        register(new DestroyMessage(), ServerMessageHandler.DestroyHandler.class, (byte)22);

        //客户端连接建立完毕
        register(new ClientConnectCompleteMessage(), ServerMessageHandler.ClientConnectCompleteHandler.class, (byte)22);
    }

    @Override
    public AbstractHandler<? extends AbstractMessage> getHandler(int messageId) {

        try {
            Constructor<? extends AbstractHandler<? extends AbstractMessage>> constructor
                    = constructorMap.get(messageId);
            AbstractHandler<? extends AbstractMessage> handler
                    = constructor.newInstance(serverMessageHandler);
            return handler;
        } catch (Exception e) {
            log.error("获取handler实例发生错误, msgId:" + messageId, e);
        }

        return null;
    }

    public <T extends AbstractMessage> void register(T message, Class<? extends AbstractHandler<T>> handlerClazz, byte queueId) {
        super.register(message, handlerClazz, queueId);
        try {
            Constructor<? extends AbstractHandler<? extends AbstractMessage>> constructor
                    = handlerClazz.getDeclaredConstructor(serverMessageHandler.getClass());
            constructorMap.put(message.getId(), constructor);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException("handler注册发生错误", e);
        }

    }
}
