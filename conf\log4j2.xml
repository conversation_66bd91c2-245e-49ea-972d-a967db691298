<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="error" monitorInterval="30">

    <!-- 日志文件常用配置  -->
    <Properties>
        <!-- 文件前缀 -->
        <Property name="APP_NAME">guandou</Property>


        <!-- 本地日志路径 -->
        <Property name="LOG_HOME">./logs/</Property>
        <!-- 压缩日志路径 -->
        <Property name="GZ_LOG_HOME">./logs/gz/</Property>
        <!-- es日志路径 -->
        <Property name="ES_LOG_HOME">/data/nbgameinfo/</Property>


        <!-- 统一日志打印格式 -->
        <Property name="LOG_PATTERN" value="[%d] [%p] [%c{1.}][%L]  [%t] %m %ex %throwable%n"/>
        <!-- JSON输出  只打印内容和换行-->
        <Property name="JSON_LOG_PATTERN"  value="%m %ex%n"/>

    </Properties>

    <Appenders>


        <!--基本console日志 begin-->
        <Console name="ErrorConsole" target="SYSTEM_OUT">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
        </Console>
        <Console name="WarnConsole" target="SYSTEM_OUT">
            <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
        </Console>
        <Console name="InfoConsole" target="SYSTEM_OUT">
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
        </Console>
        <Console name="DebugConsole" target="SYSTEM_OUT">
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
        </Console>
        <!-- 基本console日志 end-->


        <!-- 基本游戏 rollingFile日志  begin-->
        <RollingFile
                name="InfoRollingFile"
                fileName="${LOG_HOME}/${APP_NAME}.info.out"
                filePattern="${GZ_LOG_HOME}/${APP_NAME}.info.%d{yyyy-MM-dd-HH}-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>
        <RollingFile
                name="WarnRollingFile"
                fileName="${LOG_HOME}/${APP_NAME}.warn.out"
                filePattern="${GZ_LOG_HOME}/${APP_NAME}.warn.%d{yyyy-MM-dd-HH}-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>
        <RollingFile
                name="ErrorRollingFile"
                fileName="${LOG_HOME}/${APP_NAME}.error.out"
                filePattern="${GZ_LOG_HOME}/${APP_NAME}.error.%d{yyyy-MM-dd-HH}-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>
        <!-- 基本游戏rollingFile日志  end-->



        <!-- 游戏行为日志-json格式 -->
        <RollingFile
                name="GameJson"
                fileName="${LOG_HOME}/action.out"
                filePattern="${GZ_LOG_HOME}/action.out.%d{yyyy-MM-dd-HH}-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${JSON_LOG_PATTERN}" />

            <Filters>
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
        </RollingFile>



        <!--
          ***********************
          输出到ES目录的日志 begin
          ************************
         -->

        <!-- 游戏行为日志 -json -->
        <RollingFile
                name="GameJson_ES"
                fileName="${ES_LOG_HOME}/${sys:lTag}.es.log"
                filePattern="${GZ_LOG_HOME}/es-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${JSON_LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>



        <!-- 游戏info日志， 给es专用 -->
        <RollingFile
                name="InfoFile_ES"
                fileName="${ES_LOG_HOME}/${sys:lTag}.${APP_NAME}.out"
                filePattern="${GZ_LOG_HOME}/game.es-%i.gz"
                ignoreExceptions="false">
            <PatternLayout  charset="UTF-8" pattern="${LOG_PATTERN}" />
            <Filters>
                <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <Policies>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <!--
           ***********************
           输出到ES目录的日志 end
           ************************
          -->

    </Appenders>
    <Loggers>

        <!-- 消息debug 程序默认 warn -->
        <logger name="com.sh.game.common.util.DebugUtil" level="ERROR" additivity="false"/>
        <!-- class 扫描日志 -->
        <logger name="com.sh.commons.util.ClassUtil" level="ERROR" additivity="false"/>

        <!-- 游戏日志-json输出格式 -->
        <logger name="game.log.json" level="trace" additivity="false">
            <appender-ref ref="GameJson"/>
            <!--            <appender-ref ref="GameJson_ES"/>-->
        </logger>

        <root level="all">
            <appender-ref ref="InfoRollingFile"/>
            <appender-ref ref="WarnRollingFile"/>
            <appender-ref ref="ErrorRollingFile"/>
            <appender-ref ref="InfoConsole"/>
            <!--            <appender-ref ref="InfoFile_ES"/>-->
        </root>
    </Loggers>
</Configuration>
