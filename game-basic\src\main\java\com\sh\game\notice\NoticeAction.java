package com.sh.game.notice;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * notice处理action注解，有这个注解的类，将会被扫描其参数为ProcessorNotice参数的方法作为notice的执行路径
 *
 * <AUTHOR>
 * @date 2020/8/18 13:29
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
public @interface NoticeAction {
}
