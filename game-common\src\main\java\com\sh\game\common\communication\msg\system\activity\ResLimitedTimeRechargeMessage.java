package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回限时充值活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResLimitedTimeRechargeMessage extends ProtobufMessage {

    private com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge proto;

    private com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge.Builder builder;

	
	@Override
	public int getId() {
		return 4305;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ActivityProtos.ResLimitedTimeRecharge proto) {
        this.proto = proto;
    }

}
