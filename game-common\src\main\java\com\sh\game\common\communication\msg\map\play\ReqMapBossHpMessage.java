package com.sh.game.common.communication.msg.map.play;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求地图boss血量信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqMapBossHpMessage extends ProtobufMessage {

    private com.sh.game.protos.PlayProtos.ReqMapBossHp proto;

    private com.sh.game.protos.PlayProtos.ReqMapBossHp.Builder builder;

	
	@Override
	public int getId() {
		return 73002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.PlayProtos.ReqMapBossHp.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.PlayProtos.ReqMapBossHp.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.PlayProtos.ReqMapBossHp.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.PlayProtos.ReqMapBossHp getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.PlayProtos.ReqMapBossHp proto) {
        this.proto = proto;
    }

}
