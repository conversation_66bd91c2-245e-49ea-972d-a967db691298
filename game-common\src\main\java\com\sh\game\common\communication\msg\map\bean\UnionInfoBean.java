package com.sh.game.common.communication.msg.map.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class UnionInfoBean extends KryoBean {

	/**
	 * 行会唯一id
	 */
	private long uid;
	/**
	 * 行会名字
	 */
	private String name;
	/**
	 * 行会积分
	 */
	private int score;

	public long getUid() {
		return uid;
	}

	public void setUid(long uid) {
		this.uid = uid;
	}

		public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

		public int getScore() {
		return score;
	}

	public void setScore(int score) {
		this.score = score;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.uid = readLong(buf);
		this.name = readString(buf);
		this.score = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, uid);
		this.writeString(buf, name);
		this.writeInt(buf, score, false);
		return true;
	}
}
