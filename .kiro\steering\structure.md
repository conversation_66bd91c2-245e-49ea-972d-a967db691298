# 项目结构

## 根目录组织结构

```
guandou/
├── game-*                 # 游戏服务模块 (各个微服务)
├── conf/                  # 环境配置文件
├── doc/                   # 项目文档
├── logs/                  # 运行日志文件
├── release/               # 发布和部署相关
├── pom.xml                # Maven主配置文件
├── README.MD              # 项目说明文档
└── CLAUDE.md              # Claude开发规范文档
```

## 核心模块架构

### 游戏服务模块
- **game-server**: 主游戏服务器，核心游戏逻辑入口
- **game-gate**: 网关服务，处理客户端连接和协议分发
- **game-scene**: 场景服务器，管理游戏地图和场景逻辑
- **game-basic**: 基础组件模块，提供通用工具和框架
- **game-common**: 公共代码库，共享数据结构和工具类

### 专业化服务模块
- **game-pvp-battle**: PvP战斗服务器，处理玩家对战逻辑
- **game-pvp-match**: PvP匹配服务器，处理对战匹配算法
- **game-cross**: 跨服服务器，处理跨服活动和数据同步
- **game-pay**: 支付服务器，处理充值和交易逻辑

### 支持服务模块
- **game-protos**: Protocol Buffers协议定义
- **game-back-client**: 后台管理客户端
- **game-robot**: 游戏机器人，用于压力测试和自动化测试
- **game-script**: 脚本引擎，支持动态脚本执行
- **game-merge**: 服务器合并工具
- **game-all**: 完整服务器打包模块

### 打包和部署模块
- **game-package**: 各服务的打包配置
  - `game-package-game/`: 游戏服打包
  - `game-package-battle/`: 战斗服打包  
  - `game-package-match/`: 匹配服打包
  - `game-package-map/`: 场景服打包
  - `game-package-merge/`: 合服工具打包

## 子目录结构模式

### 标准Java模块结构
```
game-[module]/
├── pom.xml                    # 模块Maven配置
├── src/
│   ├── main/
│   │   ├── java/              # Java源代码
│   │   │   └── com/sh/game/   # 主包路径
│   │   └── resources/         # 配置资源文件
│   └── test/
│       └── java/              # 单元测试代码
└── target/                    # 编译输出目录
```

### 核心包路径结构
```
com.sh.game/
├── [module]/                  # 模块特定功能
├── protos/                    # 协议定义 (game-protos)
├── common/                    # 公共组件 (game-common)
├── server/                    # 服务器基础框架
├── event/                     # 事件系统
├── notice/                    # 通知系统
└── exception/                 # 异常定义
```

## 文件命名规范

### Java类命名
- **实体类**: 使用PascalCase，如 `RoleExample`, `PlayerData`
- **服务类**: 以业务名+Service结尾，如 `BattleService`, `PayService`  
- **控制器**: 以Controller结尾，如 `AdminController`, `BackendController`
- **DAO类**: 以Dao结尾，如 `PlayerDao`, `ItemDao`
- **工具类**: 以Util结尾，如 `EventUtil`, `AttributeUtil`

### 协议类命名
- **协议文件**: 以Protos结尾，如 `RoleProtos`, `BattleProtos`
- **消息类**: 按功能模块分组，使用描述性命名

### 配置文件命名
- **服务配置**: `[service].properties` 如 `game.properties`, `scene.properties`
- **数据库配置**: `db.properties`, `redis.properties`  
- **日志配置**: `log4j2.xml`, `log4j2-[service].xml`

## Import组织规范

### Import顺序
1. Java标准库 (`java.*`, `javax.*`)
2. 第三方依赖库 (按字母顺序)
3. 自研基础框架 (`com.sh.base.*`)
4. 项目内部包 (`com.sh.game.*`)

### 示例Import结构
```java
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import io.netty.channel.Channel;
import com.google.guava.collect.Maps;

import com.sh.base.commons.utils.StringUtil;
import com.sh.base.core.annotation.Component;

import com.sh.game.server.CommandProcessor;
import com.sh.game.protos.RoleProtos;
```

## 关键架构原则

### 模块分层原则
- **网关层**: game-gate处理连接管理和协议路由
- **业务层**: game-server等处理具体业务逻辑
- **数据层**: game-basic提供数据访问和持久化
- **通信层**: 基于Netty的异步网络通信

### 服务分离原则
- **按功能垂直切分**: 战斗、匹配、支付等独立服务
- **按负载水平切分**: 支持多实例部署和扩展
- **数据隔离**: 每个服务管理自己的数据范围
- **接口标准化**: 通过protobuf统一服务间通信协议

### 代码组织原则
- **单一职责**: 每个类只负责一个明确的功能
- **开放封闭**: 通过接口和抽象支持功能扩展
- **依赖倒置**: 高层模块不直接依赖底层实现细节
- **接口分离**: 提供简洁专用的接口而不是大而全的接口

### 配置管理原则
- **环境分离**: 开发、测试、生产环境配置分开管理
- **热更新支持**: 关键配置支持运行时动态更新
- **集中管理**: 通过配置中心统一管理分布式配置
- **安全存储**: 敏感信息如密码、密钥单独加密存储

### 数据访问原则
- **实体驱动**: 通过@Tag注解的实体类驱动数据操作
- **DAO模板**: 使用PersistDaoTemplate统一数据访问模式
- **缓存优先**: 优先使用Redis缓存，降低数据库压力
- **异步持久**: 通过定时任务异步将内存数据持久化到数据库

### 错误处理原则
- **分层异常**: 不同层次定义不同类型的异常
- **日志记录**: 所有异常都有对应的日志记录
- **优雅降级**: 非关键功能异常不影响核心游戏流程
- **监控告警**: 关键异常触发实时监控告警