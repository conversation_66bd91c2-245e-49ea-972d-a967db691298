package com.sh.game.remote.rpc.msg;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * DestroyMessage
 *
 * <AUTHOR>
 * @date 2020/8/28 10:45
 */
public class DestroyMessage extends AbstractMessage {

    private int moduleId;



    @Override
    public int getId() {
        return 17;
    }


    @Override
    public boolean read(KryoInput buf) {
        this.moduleId = this.readInt(buf, false);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {
        this.writeInt(buf, this.moduleId, false);
        return true;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

}
