package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p> 死亡消息（用于单独的通知对象的死亡,比如说buffer伤害等等）</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResObjectDieMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ResObjectDie proto;

    private com.sh.game.protos.FightProtos.ResObjectDie.Builder builder;

	
	@Override
	public int getId() {
		return 69006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ResObjectDie.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ResObjectDie.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ResObjectDie.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ResObjectDie getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ResObjectDie proto) {
        this.proto = proto;
    }

}
