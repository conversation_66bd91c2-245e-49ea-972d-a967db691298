package com.sh.game.remote.rpc.test;

import com.sh.game.remote.local.LocalModuleNet;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.remote.rpc.client.ModuleClientListener;
import com.sh.game.remote.rpc.server.ModuleServerListener;

/**
 * LocalTest
 *
 * <AUTHOR>
 * @date 2020/9/7 17:33
 */
public class LocalTest {

    public static void main(String[] args) throws InterruptedException {

        ModuleClient client = new ModuleClient();

        client.setModuleClientListener(new ModuleClientListener(){

            @Override
            public void afterLogin(ModuleClient client, RPCConnection connection) {

            }

            @Override
            public void serverUnavailable(ModuleClient client, RPCConnection connection) {

            }

            @Override
            public void serverDestroy(ModuleClient client, RPCConnection connection) {

            }
        });

        client.setLocalHostId(10001);

        client.setMessageReceiver(msg -> System.out.println("收到msg transform"));

        client.setNoticeReceiver(noticeTransform -> System.out.println("收到 notice transform"));



        LocalModuleNet net = new LocalModuleNet();
        net.setPort(23);
        net.setHostId(203);
        net.setMessageReceiver(msg -> {
            System.out.println("收到 msg transform");
            RPCConnection client1 = net.getClient(1);
            //client1.activeChannel().writeAndFlush(msg);
        });

        net.setNoticeReceiver(noticeTransform -> {
            System.out.println("收到 notice transform");
            RPCConnection client12 = net.getClient(10001);

            UpdateHpMpNotice notice = new UpdateHpMpNotice();
            notice.setHp(100);
            notice.setMp(100);
            notice.setRid(10001L);

            client12.transformNotice(notice, 1, 100L);
            //client12.activeChannel().writeAndFlush(noticeTransform);
        });

        net.setModuleServerListener(new ModuleServerListener() {
            @Override
            public void clientUnavailable(RPCConnection connection) {

            }

            @Override
            public void clientDestroy(RPCConnection connection) {

            }

            @Override
            public void clientConnectComplete(RPCConnection connection) {

            }
        });





        RPCConnection clientConnection = client.createLocalConnection(1, 23);

        RPCConnection serverConnection = net.createLocalConnection(clientConnection);


        net.register(clientConnection);
        client.startModule(1, serverConnection);

        serverConnection.getLogin().set(2);


        while (true) {

            Thread.sleep(2000l);
            RPCConnection connection = client.getConnection(203);
            if (connection == null) {
                continue;
            }
            UpdateHpMpNotice notice = new UpdateHpMpNotice();
            notice.setHp(100);
            notice.setMp(100);
            notice.setRid(10001L);

            connection.transformNotice(notice, 1, 100L);



        }

    }
}
