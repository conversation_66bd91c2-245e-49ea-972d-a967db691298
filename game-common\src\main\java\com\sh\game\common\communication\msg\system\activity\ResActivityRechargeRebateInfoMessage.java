package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.system.activity.bean.ActivityStatusBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回活动数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResActivityRechargeRebateInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 4092;
	}
	
	/**
	 * 充值数量
	 */
	private int recharged;
	/**
	 * 活动领取状态
	 */
	private List<ActivityStatusBean> status = new ArrayList<>();

	public int getRecharged() {
		return recharged;
	}

	public void setRecharged(int recharged) {
		this.recharged = recharged;
	}

		public List<ActivityStatusBean> getStatus() {
		return status;
	}

	public void setStatus(List<ActivityStatusBean> status) {
		this.status = status;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.recharged = readInt(buf, false);
		int statusLength = readShort(buf);
		for (int statusI = 0; statusI < statusLength; statusI++) {
			if (readByte(buf) == 0) { 
				this.status.add(null);
			} else {
				ActivityStatusBean activityStatusBean = new ActivityStatusBean();
				activityStatusBean.read(buf);
				this.status.add(activityStatusBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, recharged, false);
		writeShort(buf, this.status.size());
		for (int statusI = 0; statusI < this.status.size(); statusI++) {
			this.writeBean(buf, this.status.get(statusI));
		}
		return true;
	}
}
