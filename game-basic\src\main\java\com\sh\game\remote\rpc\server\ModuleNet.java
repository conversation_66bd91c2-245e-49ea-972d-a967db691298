package com.sh.game.remote.rpc.server;

import com.alibaba.fastjson.JSON;
import com.sh.game.remote.rpc.*;
import com.sh.game.remote.rpc.msg.ClientConnectCompleteMessage;
import com.sh.game.remote.rpc.msg.DestroyMessage;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.net.NetworkEventlistener;
import com.sh.net.NetworkService;
import com.sh.net.NetworkServiceBuilder;
import com.sh.server.Session;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.timeout.IdleState;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * ModuleNet
 *
 * <AUTHOR>
 * @date 2020/8/26 15:06
 */
@Slf4j
public class ModuleNet {

    public static final ScheduledExecutorService EXECUTOR = Executors
            .newSingleThreadScheduledExecutor(r-> new Thread(r, "module-net"));
    protected int port;

    // 创建网络服务
    NetworkService netWork;

    private int hostId;

    private NetworkServiceBuilder builder;

    protected MessageTransformReceiver messageReceiver;

    protected NoticeTransformReceiver noticeReceiver;

    protected ModuleServerListener moduleServerListener;

    protected boolean outService = false;

    protected String noticeSign;


    protected Map<Integer, RPCConnection> clientMap = new ConcurrentHashMap<>();

    public ModuleNet() {
        int bossLoopGroupCount = 4;

        int workerLoopGroupCount = Math.max(Runtime.getRuntime().availableProcessors(), 8);
        builder = new NetworkServiceBuilder();


        builder.setMsgPool(new RPCMessagePool(Collections.emptyMap()));
        builder.setBossLoopGroupCount(bossLoopGroupCount);
        builder.setWorkerLoopGroupCount(workerLoopGroupCount);
        builder.setNetworkEventlistener(new NetworkEventlistener(){

            @Override
            public void onConnected(ChannelHandlerContext ctx) {
                Channel channel = ctx.channel();
                log.info("接收到新的连接：" + channel.toString());
                if (ModuleNet.this.outService) {
                    log.error("服务已停用，关闭新连接");
                    channel.close();
                    return;
                }
                Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
                if (session == null) {
                    session = new Session();
                    session.setChannel(channel);
                    ModuleSessionValue sv = new ModuleSessionValue();
                    session.setValue(sv);
                    AttributeUtil.set(channel, ChannelAttrKey.SESSION, session);
                } else {
                    log.error("新连接建立时已存在Session，注意排查原因" + channel.toString());
                }
            }

            @Override
            public void onDisconnected(ChannelHandlerContext ctx) {
                Channel channel = ctx.channel();
                Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
                if (session == null) {
                    log.error("连接断开的时候session为null");
                    return;
                }
                unRegister(session);
                channel.closeFuture();
            }

            @Override
            public void onExceptionOccur(ChannelHandlerContext ctx, Throwable cause) {
                log.error("网络发生异常:" + ctx, cause);
            }

            @Override
            public void idle(ChannelHandlerContext ctx, IdleState state) {
                //暂时不做检查
            }
        });
        builder.setConsumer(new RPCServerConsumer(this));


    }

    public void start(int port) {
        this.port = port;
        // 创建网络服务
        builder.setPort(port);
        netWork = builder.createService();
        netWork.start();
        EXECUTOR.scheduleWithFixedDelay(() -> checkHeart(), 2000, 2000, TimeUnit.MILLISECONDS);
    }


    public void stop() {
        this.outService = true;

        RPCConnection[] connections = clientMap.values().toArray(new RPCConnection[0]);
        for (RPCConnection connection : connections) {
            DestroyMessage msg = new DestroyMessage();
            msg.setModuleId(connection.getHostId());
            ChannelFuture future = connection.activeChannel().writeAndFlush(msg);
            try {
                future.get(5000, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("发送销毁消息给对端报错", e);
            }
            connection.close();
        }

        netWork.stop();
    }


    public void register(Session session, RegisterMessage msg) {
        if (!noticeSign.equals(msg.getNoticeSign())) {
            log.error("notice sign not match. register failed. remoteIp:{}", session.getIp());

            session.close();
            return;
        }
        String host = msg.getHost();
        int port = msg.getPort();
        int moduleId = msg.getModuleId();
        int channelIndex = msg.getChannelIndex();

        if (clientMap.containsKey(moduleId)) {
            log.error("远程ip [{}]的 moduleId = {}重复，注册失败", session.getIp(), moduleId);
            msg.setRegisterFail(true);
            session.getChannel().writeAndFlush(msg);
            session.close();
            return;
        }

        RPCConnection connection = clientMap.get(moduleId);

        if (connection == null) {
            connection = new RPCConnection();
            connection.setHost(host);
            connection.setPort(port);
            connection.setHostId(moduleId);
            clientMap.put(connection.getHostId(), connection);
        }

        ModuleSessionValue value = (ModuleSessionValue) session.getValue();

        value.setHostId(moduleId);
        value.setIndex(channelIndex);

        Channel channel = session.getChannel();
        connection.setChannel(channel);

        msg.setModuleId(this.hostId);

        channel.writeAndFlush(msg);

        log.info("收到模块channel登录请求，host:{}, index:{}", connection, channelIndex);
    }

    public void unRegister(Session session) {
        ModuleSessionValue value = (ModuleSessionValue) session.getValue();
        //移除客户端
        RPCConnection rpcConnection = clientMap.remove(value.getHostId());
        this.moduleServerListener.clientUnavailable(rpcConnection);

        log.info("移除远程连接: {} {}", value.getHostId(), value.getIndex());
    }

    /**
     * 更新心跳
     * @param session
     * @param message
     */
    public void updateHeart(Session session, PingMessage message) {

        ModuleSessionValue value = (ModuleSessionValue) session.getValue();
        value.setHeart(System.currentTimeMillis());
        session.getChannel().writeAndFlush(message);
    }

    /**
     * 检查心跳
     */
    public void checkHeart() {
        try {
            long curTime = System.currentTimeMillis();
            Iterator<Map.Entry<Integer, RPCConnection>> it = clientMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<Integer, RPCConnection> next = it.next();
                RPCConnection connection = next.getValue();
                if (!connection.checkHeart(curTime)) {
                    //移除客户端换
                    it.remove();
                    log.info("客户端连接心跳检测失败，连接已经不可用，移除客户端， host:{}", connection);
                    moduleServerListener.clientUnavailable(connection);
                }
            }
        } catch (Throwable e) {
            log.error("心跳检查出错", e);
        }
    }


    /**
     * 客户端销毁
     * @param msg
     */
    public void clientDestroy(DestroyMessage msg) {
        RPCConnection connection = clientMap.remove(msg.getModuleId());
        moduleServerListener.clientDestroy(connection);
    }

    /**
     * 客户端所有通道建立完毕
     * @param msg
     */
    public void clientConnectComplete(ClientConnectCompleteMessage msg) {
        RPCConnection connection = clientMap.get(msg.getModuleId());
        int activeIndex = msg.getActiveIndex();
        connection.changeActive(activeIndex);
        log.info("客户端全部连接登录完毕, host:{}, active: {}", connection, activeIndex);
        moduleServerListener.clientConnectComplete(connection);
    }

    public Collection<RPCConnection> getAllClient() {
        return clientMap.values();
    }

    public Collection<Integer> getAllClientId() {
        List<Integer> ret = new ArrayList<>();
        clientMap.values().forEach(v->ret.add(v.getHostId()));
        return ret;
    }

    public RPCConnection getClient(int hostId) {
        return clientMap.get(hostId);
    }

    public int getOneHost() {
        if (clientMap.size() <= 0) {
            return 0;
        }

        return clientMap.entrySet().iterator().next().getKey();
    }


    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public int getHostId() {
        return hostId;
    }

    public void setHostId(int hostId) {
        this.hostId = hostId;
    }

    public ModuleServerListener getModuleServerListener() {
        return moduleServerListener;
    }

    public void setModuleServerListener(ModuleServerListener moduleServerListener) {
        this.moduleServerListener = moduleServerListener;
    }

    public MessageTransformReceiver getMessageReceiver() {
        return messageReceiver;
    }

    public void setMessageReceiver(MessageTransformReceiver messageReceiver) {
        this.messageReceiver = messageReceiver;
    }

    public NoticeTransformReceiver getNoticeReceiver() {
        return noticeReceiver;
    }

    public void setNoticeReceiver(NoticeTransformReceiver noticeReceiver) {
        this.noticeReceiver = noticeReceiver;
    }

    public String getNoticeSign() {
        return noticeSign;
    }

    public void setNoticeSign(String noticeSign) {
        this.noticeSign = noticeSign;
    }
}
