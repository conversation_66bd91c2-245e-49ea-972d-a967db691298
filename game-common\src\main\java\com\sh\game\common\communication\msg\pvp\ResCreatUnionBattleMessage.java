package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>返回帮战副本创建结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCreatUnionBattleMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125019;
	}
	
	/**
	 * 战斗id
	 */
	private int battleId;
	/**
	 * 内皇宫副本id
	 */
	private int innerMapId;
	/**
	 * 外皇宫地图id
	 */
	private int outMapId;

	public int getBattleId() {
		return battleId;
	}

	public void setBattleId(int battleId) {
		this.battleId = battleId;
	}

		public int getInnerMapId() {
		return innerMapId;
	}

	public void setInnerMapId(int innerMapId) {
		this.innerMapId = innerMapId;
	}

		public int getOutMapId() {
		return outMapId;
	}

	public void setOutMapId(int outMapId) {
		this.outMapId = outMapId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.battleId = readInt(buf, false);
		this.innerMapId = readInt(buf, false);
		this.outMapId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, battleId, false);
		this.writeInt(buf, innerMapId, false);
		this.writeInt(buf, outMapId, false);
		return true;
	}
}
