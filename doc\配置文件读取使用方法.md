# 配置数据使用



## 数据类型
- ### ConfigData
- ### CacheData

#### ConfigData 游戏从csv中加载的实体数据类型。
#### ConfigCache  从ConfigData中组装的缓存形数据类


# 使用方式

## 0 初始化
```  
//注解方式
ConfigDataManager.getInstance().init(
                ConfigDataCustomization.newInstance()
                        //设置配置文件扫描范围
                        .setPkg("com.sh.game.common.config")
                        .setSkipLine(2)
                        //设置配置检查器
                        .setChecker(ConfigLoadChecker.getInstance())
                        .setPath(option.getConfigDataPath());
                        
//xml
ConfigDataManager.getInstance().setChecker(ConfigLoadChecker.getInstance());
ConfigDataManager.getInstance().init(option.getConfigDataPath(), 2);
```

## 1  实现数据对象于缓存对象
ConfigData 继承与 AbstractConfigData,使用ConfigDataManager获取


```
@ConfigData(file="xxx", key={"id", "age"})
DemoConfigData  extends AbstractConfigData{
  private int id;
  private long age;
  private String demo;
}

//根据默认key获取配置数据类
ConfigDataManager.getInstance().getById(DemoConfigData.class,id);

//根据指定的key获取配置数据类
ConfigDataManager.getInstance().getByIdAndCacheName(DemoConfigData.class,id,"age");

//遍历获取所有数据类
ConfigDataManager.getInstance().getList(DemoConfigData.class);

```

ConfigCache 实现接口 IConfigCache，使用ConfigCacheManager获取
```
@ConfigCache
DemoConfigCache implements IConfigCache{
  
  @Override
  public void build(){
        //build....
  }
}

//根据cache类型获取缓存类
ConfigCacheManager.getInstance().getCache(DemoConfigCache.class);

```

## 2 定义

### 注解方式定义
####  注解类

##### ConfigData  类注解
###### 示例
```
@ConfigData(file=csv文件名，key=xxx)
MapConfig  extends AbstractConfigData{
  private int id;
  private String demo;
}

```
###### 注解属性详解
- file

  该配置对应的文件路径

- keys

1. 缓存的key列表，一个key构建一个缓存的map，通过key获取对应的数据。

    ```
    @ConfigData(fil="map"，keys={"id", "name")
    MapConfig  extends AbstractConfigData{
        private int id;
        private int type;
        private String name;
    }
    
    //获取id为1的MapConfig, 该方法默认使用keys数组中第一个元素作为key的名字
    ConfigDataManager.getInstance().getById(MapConfig.class, 1);
    
    //获取name为"主城"的MapConfig，该方法需要指定key的名字
    ConfigDataManager.getInstance().getByIdAndCacheName(MapConfig.class, "主城", "name");

    ```

2. key的值为配置文件的列名


3. 可以使用多个key组成一个联合key,例如 "id#age",这样将会获得一个以id和age组合为key的缓存
    ```
    @ConfigData(fil="map"，keys={"type#name")
    MapConfig  extends AbstractConfigData{
        private int id;
        private int type;
        private String name;
    }
    
    //获取 name="主城" 同时 type=2 的MapConfig
    ConfigDataManager.getInstance().getByIdAndCacheName(MapConfig.class, "主城#15", "type#name");
    
    //由于该配置文件只有一个key也可以使用默认的方法
    ConfigDataManager.getInstance().getById(MapConfig.class, "主城#15");
    
    ```
4. 不指定 keys的时候，默认是以id作为key，此时务必保证配置表中有唯一的id列
    ```
    @ConfigData(fil="map")
    MapConfig extends AbstractConfigData{
        private int id;
        private int type;
        private String name;
    }
    //获取id为1的MapConfig, 该方法默认使用keys数组中第一个元素作为key的名字
    ConfigDataManager.getInstance().getById(MapConfig.class, 1);
    ```


- checkKey

  在加载该配置文件的过程中，是否进行id唯一性检查，默认为true


- converter
  整体数据转化器，将一行csv的输入数据转换成一个MapConfigData，而不需要进行通用的数据转换过程
    ```
    //配置文件
    id, data
    1,  3#新手村
    2,  4#比奇城
    
    //地图转换器
    class MapConverter implements IConverter {
        @Override
        public Object convert(Object o) {
            Map<String, String> csvColumn = (Map<String, String>) o;
            
            MapConfig ret = new MapConfig();
            //获取id
            String id = csvColumn.get("id");
            ret.setId(Integer.parseInt(id);
  
            
            String data = csvColumn.get("data"); 
            //获取type
            String[] dataArray = data.split("#");
            ret.setType(Integer.parseInt(dataArray[0]);
            //获取name
            ret.setName(dataArray[1]);
            return ret;
        }
    }
  
  
    //使用整体转换器将map.csv 中的行数据转换成一个MapConfig
    @ConfigData(fil="map", converter=MapConverter.class)
    MapConfig extends AbstractConfigData{
        private int id;
        private int type;
        private String name;
    }
  
    //获取id为1的MapConfig, 该方法默认使用keys数组中第一个元素作为key的名字
    ConfigDataManager.getInstance().getById(MapConfig.class, 1);
    ```
- afterLoader
  当时所有该配置的所有的行读取完毕后，如果配置了afterLoader，那么会对该配置的每一行数据调用afterLoader接口。
    1. 该接口调用的时候整个系统的配置文件并没有完全读取完毕，所以只能在AfterLoader中处理当前行的数据。
    2. 该接口目前已经标记为废弃，不推荐使用，有特殊需求可以用ConfigCache实现。


###### ConfigField  属性注解
###### 示例
```
@ConfigData(file="map"，key="id")
MapConfig  extends AbstractConfigData{
  private int id;
  private int type;
  @ConfigField(column="mapName")
  private String name;
  @ConfigField(converter=ListConverter.class)
  private List<Integer> monsterList;
}
```
###### 注解属性详解
- column

  csv中的列名，如果不指定，默认为配置的属性名

- converter

  数据转换器，可选。

  将csv中的字符串数据，转混成配置的属性类型数据。
    ```
    //怪物列表转换器
    class ListConverter implements IConverter {
        @Override
        public Object convert(Object o) {
            List<Integer> ret = new ArrayList<>();
            String[] strArr = o.toString().split("#");
            for(String str : strArr){
                ret.add(Integer.parseInt(str));
            }
            return ret;
        }
    }
    ```

- ignore

  在读取过程中是否忽略该属性，默认为false


###### ConfigCache  类注解

标记一个类是否是IConfigCache

```
    @ConfigCache
    public class MapCache implements IConfigCache {
         
         Map<Integer, List<MapConfig>> map = new HashMap<>();
        
         //构建缓存
         public void build() {
            List<MapConfig> list = ConfigDataManager.getInstance().getList(MapConfig.class);
            for(MapConfig config : list){
                List<MapConfig> typeList = map.get(config.getType());
                if(typeList == null) {
                    typeList = new ArrayList<>();
                    map.put(config.getType(),typeList);
                }
                typeList.add(config);
            }
         }
         
         //自定义的业务方法
         public List<MapConfig> getTypeMapList(int type){
            return map.get(type);
         }
    } 
```


###  xml配置方式定义
```
<configs>
    //xml方式的全局afterLoader和Checker是在configdata标签中配置的。
    //注解只能在初始化参数中指定全局afterLoader和Checker。
    <configdata checker="xxx.xxx.Checker", afterloader="xxx.xxx.AfterLoader">
       配置区
    </configdata>

    <configcaches>
        缓存区
    </configcaches>
</configs>
```

#### xml和 注解的对应关系


```
@ConfigData(file="xxx" converter=MapConverter.class)

<config file="xxx" converter="MapConverter"></config, checker="XXChecker">
```

```
@ConfigData(file="xxx keys={"id", "type"}, checkKey=false)

<config file="xxx" key="id" checkKey="false">
    <caches>
        <map key="type"/>
    </caches>
    <convert field="monsterList" converter="ListConverter"/>
</config>
```

```
@ConfigField(converter=ListConverter.class, afterLoader=AfterLoader.class)

//xml方式是通过csv的列名反推java类属性名的，不支持列名和属性名不一致，如果有此种情况，请用整体转换器实现
//xml不支持column属性
//xml不支持ignore属性
//xml方式的afterLoader只能通过重写ConfigData的afterLoad()方法实现
<config>
    <!-- 这里是有需要的列才会出现在里面 -->
    <convert field="monsterList" converter="ListConverter"/>
</config>
```

```
@ConfigCache
<configcache class="MapConfigCache"></configcache>
```
