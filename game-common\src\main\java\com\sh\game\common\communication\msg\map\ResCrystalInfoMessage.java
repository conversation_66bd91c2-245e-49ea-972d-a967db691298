package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>进入副本发送副本面板信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCrystalInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResCrystalInfo proto;

    private com.sh.game.protos.MapProtos.ResCrystalInfo.Builder builder;

	
	@Override
	public int getId() {
		return 67064;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResCrystalInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResCrystalInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResCrystalInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResCrystalInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResCrystalInfo proto) {
        this.proto = proto;
    }

}
