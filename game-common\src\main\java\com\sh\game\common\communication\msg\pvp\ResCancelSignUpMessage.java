package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>跨服帮战取消报名结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCancelSignUpMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125016;
	}
	
	/**
	 * 帮会id
	 */
	private long unionId;
	/**
	 * 帮主id
	 */
	private long leaderId;
	/**
	 * 取消成功，取消失败
	 */
	private boolean state;
	/**
	 * 原因
	 */
	private String reason;

	public long getUnionId() {
		return unionId;
	}

	public void setUnionId(long unionId) {
		this.unionId = unionId;
	}

		public long getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(long leaderId) {
		this.leaderId = leaderId;
	}

		public boolean getState() {
		return state;
	}

	public void setState(boolean state) {
		this.state = state;
	}

		public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.unionId = readLong(buf);
		this.leaderId = readLong(buf);
		this.state = readBoolean(buf);
		this.reason = readString(buf);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, unionId);
		this.writeLong(buf, leaderId);
		this.writeBoolean(buf, state);
		this.writeString(buf, reason);
		return true;
	}
}
