# 砍柴次数恢复优化设计文档

## 概述

基于对现有砍柴恢复系统的深入分析，本设计文档提出了一套优化方案，旨在解决登录恢复与每秒定时器恢复之间的冲突问题，确保恢复逻辑的准确性和系统性能的优化。

### 设计目标

1. **优先级明确**: 登录恢复优先于每秒定时器恢复执行
2. **冲突避免**: 防止两种恢复机制同时作用造成的重复恢复
3. **性能优化**: 减少不必要的计算和数据库更新操作
4. **兼容性**: 保持与现有系统的完全兼容

## 架构

### 当前架构分析

```
登录事件 → onRoleLogin() → calLiXianKanChaiCount()
    ↓
设置 recordTime = 当前时间
    ↓
计算离线恢复数量 → 更新 kanCaiLimit

每秒定时器 → onRoleSecond() 
    ↓
检查 recordTime 是否在3秒内
    ↓
如果是 → 跳过恢复
如果否 → 执行正常每秒恢复
```

### 优化后的架构设计

```
登录事件流程:
Player Login → RenderScript.onRoleLogin()
    ↓
calLiXianKanChaiCount() 执行优先级检查
    ↓
计算完整离线恢复 + 设置恢复标记
    ↓
更新数据并记录日志

每秒定时器流程:
Timer Tick → RenderScript.onRoleSecond()
    ↓
检查登录恢复标记 (recordTime)
    ↓
如果冲突 → 跳过 (避免重复恢复)
如果正常 → 执行单次恢复 + 更新时间戳
```

## 组件和接口

### 1. 核心修改组件

#### RenderScript.java
- **文件路径**: `game-script/src/main/java/com/sh/game/script/render/RenderScript.java`
- **修改方法**:
  - `calLiXianKanChaiCount()`: 增强登录恢复逻辑
  - `onRoleSecond()`: 添加冲突检测机制

#### 关键接口保持不变
- `IRenderScript`: 保持现有接口契约
- `IEventOnRoleLoginScript`: 登录事件接口
- `IRoleOnSecondScript`: 每秒定时器接口

### 2. 数据结构利用

#### RenderData实体
- **现有字段利用**:
  - `kanCaiLimit.first`: 当前砍柴次数
  - `kanCaiLimit.second`: 最后恢复时间戳
  - `recordTime`: 登录恢复标记时间戳

#### 配置常量
- `GameConst.GlobalId.KAN_CHAI_RECOVER_TIME`: 恢复间隔(30秒)
- `StructureConfig.shangxian`: 砍柴次数上限

## 数据模型

### 恢复状态模型

```java
// 现有数据结构，无需修改
public class RenderData {
    // 砍柴次数限制 <当前次数, 最后恢复时间>
    private TwoTuple<Integer, Integer> kanCaiLimit;
    
    // 记录时间 (用作登录恢复标记)
    private int recordTime;
    
    // 其他现有字段...
}
```

### 恢复逻辑状态图

```
登录恢复状态:
[登录] → [计算离线时间] → [批量恢复] → [设置标记] → [完成]
   ↓
recordTime = 当前时间戳

每秒恢复状态:
[定时器] → [检查标记] → [判断冲突]
                    ↓
            [有冲突] → [跳过]
            [无冲突] → [单次恢复] → [更新时间]
```

## 错误处理

### 1. 时间同步错误处理

```java
// 处理时间回退或异常情况
if (nowOfSeconds < renderData.getRecordTime()) {
    log.warn("时间异常检测,重置恢复标记");
    renderData.setRecordTime(0);
}
```

### 2. 数据一致性保护

```java
// 防止恢复次数超过上限
int finalCount = Math.min(
    currentCount + recoveryCount, 
    structureLimit + cardBonus
);
```

### 3. 异常情况日志记录

- **登录恢复异常**: 记录玩家ID、异常原因、恢复前后状态
- **每秒恢复跳过**: 可选记录，避免日志过多
- **数据更新失败**: 记录ERROR级别日志

## 测试策略

### 1. 单元测试

#### 测试用例1: 登录恢复优先级
```java
@Test
public void testLoginRecoveryPriority() {
    // 模拟玩家离线2小时
    // 验证登录时正确计算恢复次数
    // 验证recordTime设置正确
}
```

#### 测试用例2: 冲突避免机制
```java
@Test  
public void testConflictAvoidance() {
    // 执行登录恢复
    // 立即触发每秒恢复
    // 验证每秒恢复被正确跳过
}
```

#### 测试用例3: 边界条件处理
```java
@Test
public void testEdgeCases() {
    // 测试时间边界(3秒临界点)
    // 测试恢复次数上限
    // 测试异常时间戳处理
}
```

### 2. 集成测试

#### 场景测试1: 正常登录流程
- 玩家正常登录 → 离线恢复执行 → 每秒恢复正常跳过3秒 → 后续每秒恢复正常

#### 场景测试2: 长时间离线
- 玩家离线超过最大时间 → 登录恢复计算正确 → 不超过上限

#### 场景测试3: 频繁登录
- 短时间内多次登录 → 每次登录恢复计算正确 → 无重复恢复

### 3. 性能测试

#### 负载测试
- 同时1000个玩家登录，验证性能影响
- 每秒定时器处理性能对比

#### 数据库压力测试  
- 验证频繁的DataCenter.updateData()调用不会造成性能瓶颈

### 4. 回归测试

- 确保快速砍柴功能不受影响
- 确保砍柴消耗逻辑不受影响
- 确保收益计算逻辑不受影响
- 确保相关UI显示正常

## 实施计划

### 阶段1: 核心逻辑修改
1. 修改`calLiXianKanChaiCount()`方法，增加恢复前后计数和日志
2. 设置`recordTime`标记机制
3. 修改`onRoleSecond()`方法，增加冲突检测逻辑

### 阶段2: 测试验证
1. 编写并执行单元测试
2. 进行集成测试
3. 性能测试和优化

### 阶段3: 部署和监控
1. 灰度部署，监控关键指标
2. 日志分析，确保功能正常
3. 全量部署

## 设计决策和理由

### 决策1: 使用recordTime字段作为标记
**理由**: 
- 利用现有字段，无需修改数据结构
- recordTime在登录时已被重置为0，适合改造
- 提供精确的时间戳比较能力

### 决策2: 设置3秒的冲突检测窗口
**理由**:
- 足够覆盖登录处理的时间延迟
- 不会影响正常的每秒恢复逻辑
- 在性能和功能之间取得平衡

### 决策3: 保持现有接口不变
**理由**:
- 确保与其他系统的兼容性
- 降低修改风险和测试复杂度
- 遵循最小修改原则

### 决策4: 增强日志记录
**理由**:
- 便于问题排查和系统监控
- 提供运营数据支持
- 帮助验证功能正确性