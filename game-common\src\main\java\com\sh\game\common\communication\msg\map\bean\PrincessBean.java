package com.sh.game.common.communication.msg.map.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class PrincessB<PERSON> extends KryoBean {

	/**
	 * 公主唯一怪物id
	 */
	private long mid;
	/**
	 * 怪物id
	 */
	private int cfgId;
	/**
	 * 0:未刷新 1:已刷新 2:护送中 3:已送达
	 */
	private int state;
	/**
	 * 刷新时间
	 */
	private int time;
	/**
	 * x
	 */
	private int x;
	/**
	 * y
	 */
	private int y;
	/**
	 * 归属id
	 */
	private long owner;

	public long getMid() {
		return mid;
	}

	public void setMid(long mid) {
		this.mid = mid;
	}

		public int getCfgId() {
		return cfgId;
	}

	public void setCfgId(int cfgId) {
		this.cfgId = cfgId;
	}

		public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

		public int getTime() {
		return time;
	}

	public void setTime(int time) {
		this.time = time;
	}

		public int getX() {
		return x;
	}

	public void setX(int x) {
		this.x = x;
	}

		public int getY() {
		return y;
	}

	public void setY(int y) {
		this.y = y;
	}

		public long getOwner() {
		return owner;
	}

	public void setOwner(long owner) {
		this.owner = owner;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.mid = readLong(buf);
		this.cfgId = readInt(buf, false);
		this.state = readInt(buf, false);
		this.time = readInt(buf, false);
		this.x = readInt(buf, false);
		this.y = readInt(buf, false);
		this.owner = readLong(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, mid);
		this.writeInt(buf, cfgId, false);
		this.writeInt(buf, state, false);
		this.writeInt(buf, time, false);
		this.writeInt(buf, x, false);
		this.writeInt(buf, y, false);
		this.writeLong(buf, owner);
		return true;
	}
}
