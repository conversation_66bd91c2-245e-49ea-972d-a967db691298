package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ItemFromBean extends KryoBean {

	/**
	 * 产生行为 1：打怪掉落  0：其他来源
	 */
	private int action;
	/**
	 * 参数
	 */
	private List<String> params = new ArrayList<>();

	public int getAction() {
		return action;
	}

	public void setAction(int action) {
		this.action = action;
	}

		public List<String> getParams() {
		return params;
	}

	public void setParams(List<String> params) {
		this.params = params;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.action = readInt(buf, false);
		int paramsLength = readShort(buf);
		for (int paramsI = 0; paramsI < paramsLength; paramsI++) {
			this.params.add(this.readString(buf));
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, action, false);
		writeShort(buf, this.params.size());
		for (int paramsI = 0; paramsI < this.params.size(); paramsI++) {
			this.writeString(buf, this.params.get(paramsI));
		}
		return true;
	}
}
