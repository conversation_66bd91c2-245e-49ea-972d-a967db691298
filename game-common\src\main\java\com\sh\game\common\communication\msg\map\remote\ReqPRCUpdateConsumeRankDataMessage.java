package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求更新消费数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqPRCUpdateConsumeRankDataMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData proto;

    private com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData.Builder builder;

	
	@Override
	public int getId() {
		return 82018;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ReqPRCUpdateConsumeRankData proto) {
        this.proto = proto;
    }

}
