package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class LogBean extends KryoBean {

	/**
	 * 玩家
	 */
	private RoleBriefBean role;
	/**
	 * 道具
	 */
	private ItemBriefBean item;

	public RoleBriefBean getRole() {
		return role;
	}

	public void setRole(RoleBriefBean role) {
		this.role = role;
	}

		public ItemBriefBean getItem() {
		return item;
	}

	public void setItem(ItemBriefBean item) {
		this.item = item;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		if (readByte(buf) != 0) {
			RoleBriefBean roleBriefBean = new RoleBriefBean();
			roleBriefBean.read(buf);
			this.role = roleBriefBean;
		}
		if (readByte(buf) != 0) {
			ItemBriefBean itemBriefBean = new ItemBriefBean();
			itemBriefBean.read(buf);
			this.item = itemBriefBean;
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeBean(buf, role);
		this.writeBean(buf, item);
		return true;
	}
}
