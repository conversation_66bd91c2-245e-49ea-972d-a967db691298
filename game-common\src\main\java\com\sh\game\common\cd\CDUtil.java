package com.sh.game.common.cd;

import com.sh.commons.util.Cast;
import com.sh.game.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * cd工具类
 */
@Slf4j
public class CDUtil {

    public static void addCd(CDObject cdObject, int cdType, int key, long endTime) {
        Map<Long, CD> cdMap = cdObject.getCdMap();
        long cdKey = getKey(cdType, key);
        CD cd = new CD();
        cd.setEndTime(endTime);
        cd.setKey(key);
        cd.setType(cdType);
        cdMap.put(cdKey, cd);
    }

    public static long getCd(CDObject cdObject, int cdType, int key) {
        Map<Long, CD> cdMap = cdObject.getCdMap();
        long cdKey = getKey(cdType, key);
        CD cd = cdMap.get(cdKey);
        if (cd != null) {
            return cd.getEndTime();
        }
        return 0;
    }

    public static boolean isCool(CDObject cdObject, int cdType, int key, long curTime) {
        Map<Long, CD> cdMap = cdObject.getCdMap();
        long cdKey = getKey(cdType, key);
        CD cd = cdMap.get(cdKey);

        if (cd != null) {
            return curTime >= cd.getEndTime();
        }
        return true;
    }

    public static boolean isCool(CDObject cdObject, int cdType, int key) {
        return isCool(cdObject, cdType, key, TimeUtil.getNowOfMills());
    }

    public static boolean removeCd(CDObject cdObject, int cdType, int key) {
        Map<Long, CD> cdMap = cdObject.getCdMap();
        long cdKey = getKey(cdType, key);
        CD removeCd = cdMap.remove(cdKey);
        return removeCd != null;
    }

    private static long getKey(int cdType, int key) {
        return Cast.combineInt2Long(key, cdType);
    }

}
