package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>游戏服之间的心跳</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqGameHeartMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125007;
	}
	
	/**
	 * 服务器id
	 */
	private int serverId;
	/**
	 * 平台id
	 */
	private int pid;

	public int getServerId() {
		return serverId;
	}

	public void setServerId(int serverId) {
		this.serverId = serverId;
	}

		public int getPid() {
		return pid;
	}

	public void setPid(int pid) {
		this.pid = pid;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.serverId = readInt(buf, false);
		this.pid = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, serverId, false);
		this.writeInt(buf, pid, false);
		return true;
	}
}
