package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class RandAttributeBean extends KryoBean {

	/**
	 * 职业限定
	 */
	private int career;
	/**
	 * 属性类型
	 */
	private int attributeType;
	/**
	 * 属性值
	 */
	private long attributeValue;

	public int getCareer() {
		return career;
	}

	public void setCareer(int career) {
		this.career = career;
	}

		public int getAttributeType() {
		return attributeType;
	}

	public void setAttributeType(int attributeType) {
		this.attributeType = attributeType;
	}

		public long getAttributeValue() {
		return attributeValue;
	}

	public void setAttributeValue(long attributeValue) {
		this.attributeValue = attributeValue;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.career = readInt(buf, false);
		this.attributeType = readInt(buf, false);
		this.attributeValue = readLong(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, career, false);
		this.writeInt(buf, attributeType, false);
		this.writeLong(buf, attributeValue);
		return true;
	}
}
