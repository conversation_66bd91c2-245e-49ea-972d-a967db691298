package com.sh.game;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.UnpooledByteBufAllocator;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelOutboundBuffer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.DefaultChannelPromise;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.util.concurrent.EventExecutor;
import io.netty.util.concurrent.Future;
import io.netty.util.concurrent.GenericFutureListener;
import io.netty.util.internal.PlatformDependent;
import sun.misc.JavaNioAccess;
import sun.misc.SharedSecrets;

import java.io.UnsupportedEncodingException;
import java.lang.management.BufferPoolMXBean;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicLongFieldUpdater;

/**
 * <AUTHOR>
 * @date 2019/6/25 16:12
 */
public class WebSocketServer {

    static private Map<Channel, ScheduledFuture<?>> futureMap = new ConcurrentHashMap<>();

    static private ByteBufAllocator ALLOCATOR = new UnpooledByteBufAllocator(false);

    final static ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    private static AtomicLongFieldUpdater<ChannelOutboundBuffer> updater;

    private static final AtomicLong directMemory;

    static {

        try {
            Class c = Class.forName("io.netty.channel.ChannelOutboundBuffer");
            Field field = c.getDeclaredField("TOTAL_PENDING_SIZE_UPDATER");
            field.setAccessible(true);
            updater = (AtomicLongFieldUpdater<ChannelOutboundBuffer>) field.get(ChannelOutboundBuffer.class);

            Class c1 = Class.forName("io.netty.util.internal.PlatformDependent");

            Field DIRECT_MEMORY_LIMIT = c1.getDeclaredField("DIRECT_MEMORY_LIMIT");
            DIRECT_MEMORY_LIMIT.setAccessible(true);
            System.out.println("最大堆外内存限制：" + (long)DIRECT_MEMORY_LIMIT.get(PlatformDependent.class)
            /1024/1024);

            Field field1 = c1.getDeclaredField("DIRECT_MEMORY_COUNTER");
            field1.setAccessible(true);
            directMemory = (AtomicLong) field1.get(PlatformDependent.class);
            scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
                @Override
                public void run() {
                    //int memoryInKb = (int) (directMemory.get() / 1024);
                    //System.out.println("netty堆外内存使用: "+memoryInKb+" kB");
                    /*System.out.println("--------------------------------------");
                    System.out.println("连接数：\t" + futureMap.size());
                    System.out.println("jvm最大内存：\t" + Runtime.getRuntime().maxMemory() / 1024 / 1024 + "M");
                    System.out.println("jvm总内存：\t" + Runtime.getRuntime().totalMemory()/ 1024 / 1024+ "M");
                    System.out.println("jvm可用：\t" + Runtime.getRuntime().freeMemory()/ 1024 / 1024+ "M");
                    MemoryMXBean m = ManagementFactory.getMemoryMXBean();
                    long committed = m.getNonHeapMemoryUsage().getCommitted();
                    long init = m.getNonHeapMemoryUsage().getInit();
                    long used = m.getNonHeapMemoryUsage().getUsed();
                    long max = m.getNonHeapMemoryUsage().getMax();
                    System.out.println("No-Heap:");
                    System.out.println("  --init:\t" + init / 1024/1024 + "M");
                    System.out.println("  --committed:\t" + committed / 1024/1024 + "M");
                    System.out.println("  --used:\t" + used / 1024/1024 + "M");
                    System.out.println("  --max:\t" + max / 1024/1024 + "M");

                    System.out.println("Heap:");
                    System.out.println("  --init:\t" + m.getHeapMemoryUsage().getInit() / 1024/1024 + "M");
                    System.out.println("  --committed:\t" + m.getHeapMemoryUsage().getCommitted() / 1024/1024 + "M");
                    System.out.println("  --used:\t" + m.getHeapMemoryUsage().getUsed() / 1024/1024 + "M");
                    System.out.println("  --max:\t" + m.getHeapMemoryUsage().getMax() / 1024/1024 + "M");


                    BufferPoolMXBean directBufferPoolMBean = getDirectBufferPoolMBean();
                    System.out.println("DirectBufferPool:");
                    System.out.println("  --MemoryUsed:\t" + directBufferPoolMBean.getMemoryUsed() / 1024/1024 + "M");
                    System.out.println("  --TotalCapacity:\t" + directBufferPoolMBean.getTotalCapacity() / 1024/1024 + "M");
                    System.out.println("  --Count:\t" + directBufferPoolMBean.getCount());*/


                    /*JavaNioAccess.BufferPool nioBufferPool = getNioBufferPool();
                    System.out.println("NioBufferPool:");
                    System.out.println("  --MemoryUsed:\t" + nioBufferPool.getMemoryUsed());
                    System.out.println("  --TotalCapacity:\t" + nioBufferPool.getTotalCapacity());
                    System.out.println("  --Count:\t" + nioBufferPool.getCount());*/
                }
            }, 1000, 1000, TimeUnit.MILLISECONDS);



        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static BufferPoolMXBean getDirectBufferPoolMBean(){
        return ManagementFactory.getPlatformMXBeans(BufferPoolMXBean.class)
                .stream()
                .filter(e -> e.getName().equals("direct"))
                .findFirst()
                .orElseThrow(()->new RuntimeException());
    }

    public static JavaNioAccess.BufferPool getNioBufferPool(){
        return SharedSecrets.getJavaNioAccess().getDirectBufferPool();
    }

    public static void main(String[] args) throws InterruptedException {
        NioEventLoopGroup bossGroup = new NioEventLoopGroup(2);
        NioEventLoopGroup workerGroup = new NioEventLoopGroup(4);
        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup);

        bootstrap.channel(NioServerSocketChannel.class);
        bootstrap.option(ChannelOption.SO_BACKLOG, 1024);
        //bootstrap.childOption(ChannelOption.SO_RCVBUF, 128 * 1024);
        //bootstrap.childOption(ChannelOption.SO_SNDBUF, 128 * 1024);
        bootstrap.handler(new LoggingHandler(LogLevel.DEBUG));
        //bootstrap.childOption(ChannelOption.WRITE_BUFFER_WATER_MARK, new WriteBufferWaterMark(128, 512));
        bootstrap.childHandler(new ChannelInitializer<SocketChannel>() {

            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                ChannelPipeline pip = ch.pipeline();
                ch.config().setAllocator(ALLOCATOR);
                //添加websocket相关内容
                /*pip.addLast(new HttpServerCodec());
                pip.addLast(new HttpObjectAggregator(65536));
                pip.addLast(new WebSocketServerProtocolHandler("/", true));
                pip.addLast(new WebSocketDecoder());
                pip.addLast(new WebSocketEncoder());*/
                pip.addLast(new StringEncoder(Charset.forName("UTF8")));
                pip.addLast(new StringDecoder(Charset.forName("UTF8")));

                pip.addLast(new ChannelInboundHandlerAdapter(){
                    @Override
                    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                        super.channelRead(ctx, msg);
                        System.out.println(msg);
                    }

                    @Override
                    public void channelActive(ChannelHandlerContext ctx) {
                        System.out.println("接受到连接");
                        ScheduledFuture<?> scheduledFuture = scheduledExecutorService.scheduleAtFixedRate(
                                new Consumer(ctx), 500, 10, TimeUnit.MILLISECONDS);
                        //InfoTask command = new InfoTask();
                        //command.ch = ch;
                        //scheduledExecutorService.scheduleAtFixedRate(command, 1000, 1000, TimeUnit.MILLISECONDS);

                        futureMap.put(ctx.channel(), scheduledFuture);
                    }


                    @Override
                    public void channelInactive(ChannelHandlerContext ctx) {
                        ScheduledFuture<?> scheduledFuture = futureMap.get(ctx.channel());
                        if (scheduledFuture != null) {
                            System.out.println("取消定时任务...");
                            scheduledFuture.cancel(true);
                        }
                    }
                });
            }
        });


        ChannelFuture f = bootstrap.bind(8888);
        f.sync();


        while(true) {
            Thread.sleep(1000000L);
        }


    }
    static String ack = "If you are sure that the frame and its content are not accessed " +
            "\nafter the current decode(ChannelHandlerContext, ByteBuf) call returns, you " +
            "\ncan even avoid memory copy by returning the sliced sub-region (i.e. return " +
            "\nbuffer.slice(index, length)). It's often useful when you convert the extracted " +
            "\nframe into an object. Refer to the source code of ObjectDecoder to see how this " +
            "\nmethod is overridden to avoid memory copy.";

    static AtomicInteger count = new AtomicInteger(1);



    static class Consumer implements Runnable {

        static private int submitCount;

        static private int sendedCount;

        private ChannelHandlerContext ctx;

        private Channel ch;

        public Consumer(ChannelHandlerContext ctx) {
            this.ctx = ctx;
            this.ch = ctx.channel();

        }

        @Override
        public void run() {
            int writeBufferHighWaterMark = ch.config().getWriteBufferHighWaterMark();

            //System.out.println(ch.unsafe().outboundBuffer().nioBufferSize() + "/" + writeBufferHighWaterMark);
            String msg = ack + "\n " + count.getAndIncrement();
            byte[] bytes;
            try {
                bytes = msg.getBytes("utf-8");
            } catch (UnsupportedEncodingException e) {
                bytes = new byte[0];
            }
            ByteBuf by = ch.alloc().buffer(bytes.length);
            by.writeBytes(bytes);
            /*if (!ch.isWritable()) {
                //System.out.println("到达高水位..");
                return;
            }*/
            //submitCount += by.readableBytes();
            //System.out.println("已提交：" + submitCount);


                //System.out.println("发送消息");
            //MyPromise promise = new MyPromise(ch, ch.eventLoop());
            ch.writeAndFlush(by);
            /*promise.addListener(new MyListener(bytes.length) {
                @Override
                public void operationComplete(Future<? super Void> future) throws Exception {
                    sendedCount += this.byteCount;
                    //System.out.println("已发送：" + sendedCount);
                }
            });*/

        }
    }

    static abstract class MyListener implements GenericFutureListener<Future<? super Void>> {

        protected int byteCount;

        public MyListener(int byteCount) {
            this.byteCount = byteCount;
        }

    }

    static class InfoTask implements Runnable {
        Channel ch;

        @Override
        public void run() {
            System.out.println("-----------------------------------------------------------------------");



            if (!ch.isWritable()) {
                System.out.println("channel到达高水位.");
            }
            System.out.println("SO_SEND_BUFF：" + ch.config().getOption(ChannelOption.SO_SNDBUF).intValue());
            System.out.println("未flush字节：" + (Consumer.submitCount - Consumer.sendedCount));
            System.out.println("submitted：" + Consumer.submitCount + ", flushed：" + Consumer.sendedCount);
            System.out.println("netty outbound buffer size: " + updater.get(ch.unsafe().outboundBuffer()));
            System.out.println();



        }
    }

    static class MyPromise extends DefaultChannelPromise {

        public MyPromise(Channel channel, EventExecutor executor) {
            super(channel, executor);
        }
    }
}




