package com.sh.game.common.communication.msg.map.move;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>通知奔跑</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRunMessage extends ProtobufMessage {

    private com.sh.game.protos.MoveProtos.ResRun proto;

    private com.sh.game.protos.MoveProtos.ResRun.Builder builder;

	
	@Override
	public int getId() {
		return 68005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MoveProtos.ResRun.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MoveProtos.ResRun.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MoveProtos.ResRun.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MoveProtos.ResRun getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MoveProtos.ResRun proto) {
        this.proto = proto;
    }

}
