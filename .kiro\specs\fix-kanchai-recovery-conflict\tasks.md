# 实现计划

## 总体任务概述
修复RenderScript.java中砍柴次数恢复机制的冲突问题，通过实施时间窗口锁定机制确保登录恢复和每秒恢复的协调工作。

---

## 第一阶段：冲突防护机制实现

- [ ] **1. 实现基础冲突检测逻辑**
  - 在RenderScript.java中添加冲突窗口常量：`private static final int CONFLICT_WINDOW_SECONDS = 3;`
  - 实现`isInConflictWindow(RenderData renderData, int currentTime)`方法检查是否在冲突期内
  - 添加单元测试验证冲突检测逻辑的准确性（边界条件测试）
  - 测试用例：recordTime为0、时间差为3秒、时间差为4秒的各种场景
  - _需求映射: 1.1, 1.3_

- [ ] **2. 优化登录恢复方法calLiXianKanChaiCount**
  - 修改`calLiXianKanChaiCount`方法，在开始执行时设置recordTime为当前时间
  - 在恢复完成后确保kanCaiLimit.second也更新为当前时间
  - 添加冲突防护日志记录，记录恢复前后的数量变化和执行时间
  - 重构现有的日志输出，增加更详细的调试信息
  - _需求映射: 1.1, 1.4, 4.1_

- [ ] **3. 优化每秒恢复方法onRoleSecond** 
  - 修改`onRoleSecond`方法，在执行恢复前调用`isInConflictWindow`检查
  - 如果在冲突期内，跳过执行并记录info级别日志说明跳过原因
  - 如果不在冲突期内，正常执行恢复逻辑
  - 确保恢复后正确更新kanCaiLimit.second时间戳
  - _需求映射: 1.2, 1.3, 2.2_

---

## 第二阶段：时间计算和上限控制优化

- [ ] **4. 增强时间计算准确性**
  - 在`calLiXianKanChaiCount`方法中添加时间差验证逻辑
  - 如果离线时间为负数或零，记录error日志并跳过恢复
  - 优化时间差计算，确保使用正确的登出时间和当前时间
  - 添加异常时间戳的安全处理机制
  - _需求映射: 2.1, 2.3, 2.5_

- [ ] **5. 强化上限控制逻辑**
  - 在两个恢复方法中都添加上限检查，确保不超过`config.getShangxian() + cardAddKanChaiLimit(role)`
  - 当达到上限时，记录warn级别日志并提前返回
  - 优化恢复数量计算，使用`Math.min(calculatedAmount, availableSpace)`确保不超限
  - 添加上限配置异常的安全处理
  - _需求映射: 3.1, 3.2, 3.3, 3.5_

---

## 第三阶段：数据一致性和状态管理

- [ ] **6. 实现数据状态同步管理**
  - 为登录恢复添加synchronized关键字确保线程安全
  - 为每秒恢复添加synchronized关键字确保线程安全  
  - 在两个方法中确保数据更新的原子性，避免部分更新
  - 添加数据一致性校验，检查kanCaiLimit数据的合理性
  - _需求映射: 4.1, 4.2, 4.3_

- [ ] **7. 优化错误处理和数据回滚**
  - 为恢复操作添加try-catch异常处理
  - 实现恢复失败时的数据回滚机制
  - 添加详细的异常日志记录，包含玩家ID、恢复类型、错误原因
  - 确保异常情况下数据状态的一致性
  - _需求映射: 4.4, 5.3, 5.4_

---

## 第四阶段：日志和监控增强

- [ ] **8. 完善日志记录系统**
  - 标准化恢复成功日志格式：`砍柴恢复成功-类型:{},roleId:{},恢复前:{},恢复后:{},恢复量:{},时间:{}`
  - 添加冲突检测日志：`砍柴恢复冲突检测-roleId:{},登录恢复时间:{},当前时间:{},时间差:{}`
  - 增加数据异常警告日志：`砍柴数据一致性异常-roleId:{},当前数量:{},上限:{},时间戳:{}`
  - 实现恢复跳过日志：记录跳过原因和相关上下文信息
  - _需求映射: 5.1, 5.2, 5.5_

- [ ] **9. 添加性能监控指标**
  - 在关键恢复方法中添加执行时间统计
  - 记录恢复操作的频率和成功率
  - 监控冲突跳过的频率，如果过高需要告警
  - 添加内存使用和性能影响的监控点
  - _需求映射: 6.1, 6.2, 6.4_

---

## 第五阶段：测试验证和集成

- [ ] **10. 实现单元测试**
  - 创建RenderScriptTest测试类
  - 测试冲突检测逻辑的各种时间窗口场景
  - 测试登录恢复在不同离线时间下的行为
  - 测试每秒恢复在冲突期内外的不同表现
  - 测试上限控制在各种边界条件下的正确性
  - _需求映射: 全部需求验证_

- [ ] **11. 进行并发测试**
  - 模拟玩家同时登录和每秒恢复的并发场景
  - 使用多线程测试数据一致性
  - 验证synchronized机制的有效性
  - 测试高并发下的性能表现
  - _需求映射: 4.5, 6.3, 6.5_

- [ ] **12. 集成测试和最终验证**
  - 在测试环境中部署修改后的代码
  - 验证现有游戏功能不受影响
  - 测试玩家登录和砍柴功能的正常工作
  - 收集性能数据，确保符合设计目标
  - 准备生产环境部署的回滚方案
  - _需求映射: 全部需求最终验证_

---

## 实现注意事项

### 代码修改策略
1. **最小侵入性原则**: 尽量在现有方法中添加逻辑，避免大幅重构
2. **向后兼容**: 确保修改不影响现有功能的正常运行
3. **渐进式部署**: 支持通过配置开关控制新功能的启用

### 测试验证重点
1. **时间边界测试**: 特别关注3秒冲突窗口的边界条件
2. **并发安全测试**: 验证多线程环境下的数据一致性
3. **性能影响测试**: 确保修改不会显著影响游戏性能

### 部署和监控
1. **灰度发布**: 先在少量服务器上验证，再全面推广
2. **监控告警**: 设置关键指标的监控和告警阈值
3. **快速回滚**: 准备紧急情况下的快速回滚方案