package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.RPCConnection;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;


/**
 * Created by 张力 on 2017/6/30.
 */
public class ChannelConnectListener implements ChannelFutureListener {

    private RPCConnection connection;

    private int index;
    public static final Logger LOGGER = LoggerFactory.getLogger(ChannelConnectListener.class);

    public ChannelConnectListener(RPCConnection connection, int index) {
        this.connection = connection;
        this.index = index;
    }

    @Override
    public void operationComplete(ChannelFuture future) {

        if (future.isSuccess()) {
            Channel channel = future.channel();
//            connection.getChannels()[index] = channel;
            connection.setChannel(channel);
            channel.closeFuture().addListener(new ChannelDisconnectedListener(connection, index));
            connection.resetReconnectDelay(index);
            connection.getClient().login(connection, channel, index);

            LOGGER.error("服务器连接成功[{}:{}/{}]", connection.getHost(), connection.getPort(), index);
            return;
        }
        if (connection.isDestroy()) {
            LOGGER.error("服务器连接失败[{}:{}/{}]，客户端已关闭.", connection.getHost(), connection.getPort(), index);
            return;
        }

        int reconnectDelay = connection.getReconnectDelay(index);
        LOGGER.error("服务器连接失败[{}:{}/{}]，将在{}秒后进行重连...", connection.getHost(), connection.getPort(), index, reconnectDelay);
        ModuleClient.EXECUTOR.schedule(new Runnable() {
            @Override
            public void run() {
                connection.getClient().connect(index, connection);
            }
        }, reconnectDelay, TimeUnit.SECONDS);


    }
}
