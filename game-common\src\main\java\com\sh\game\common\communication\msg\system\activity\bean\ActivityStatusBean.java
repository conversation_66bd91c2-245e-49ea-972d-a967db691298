package com.sh.game.common.communication.msg.system.activity.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ActivityStatusBean extends KryoBean {

	/**
	 * 
	 */
	private int cid;
	/**
	 * 
	 */
	private int status;

	public int getCid() {
		return cid;
	}

	public void setCid(int cid) {
		this.cid = cid;
	}

		public int getStatus() {
		return status;
	}

	public void setStatus(int status) {
		this.status = status;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.cid = readInt(buf, false);
		this.status = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, cid, false);
		this.writeInt(buf, status, false);
		return true;
	}
}
