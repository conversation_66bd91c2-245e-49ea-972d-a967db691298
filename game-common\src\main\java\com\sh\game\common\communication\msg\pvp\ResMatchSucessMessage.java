package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.BattlePlayerBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>匹配成功</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResMatchSucessMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125005;
	}
	
	/**
	 * 匹配组列表
	 */
	private List<Long> groupId = new ArrayList<>();
	/**
	 * 战斗服id
	 */
	private int hostId;
	/**
	 * pvp类型
	 */
	private int matchType;
	/**
	 * 比赛服IP地址
	 */
	private String ip;
	/**
	 * 比赛服端口
	 */
	private int port;
	/**
	 * 地图id
	 */
	private int mapId;
	/**
	 * 地图配置id
	 */
	private int cfgId;
	/**
	 * 匹配的玩家列表
	 */
	private List<BattlePlayerBean> playerList = new ArrayList<>();

	public List<Long> getGroupId() {
		return groupId;
	}

	public void setGroupId(List<Long> groupId) {
		this.groupId = groupId;
	}
	public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

		public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

		public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

		public int getMapId() {
		return mapId;
	}

	public void setMapId(int mapId) {
		this.mapId = mapId;
	}

		public int getCfgId() {
		return cfgId;
	}

	public void setCfgId(int cfgId) {
		this.cfgId = cfgId;
	}

		public List<BattlePlayerBean> getPlayerList() {
		return playerList;
	}

	public void setPlayerList(List<BattlePlayerBean> playerList) {
		this.playerList = playerList;
	}

	@Override
	public boolean read(KryoInput buf) {

		int groupIdLength = readShort(buf);
		for (int groupIdI = 0; groupIdI < groupIdLength; groupIdI++) {
			this.groupId.add(this.readLong(buf));
		}
		this.hostId = readInt(buf, false);
		this.matchType = readInt(buf, false);
		this.ip = readString(buf);
		this.port = readInt(buf, false);
		this.mapId = readInt(buf, false);
		this.cfgId = readInt(buf, false);
		int playerListLength = readShort(buf);
		for (int playerListI = 0; playerListI < playerListLength; playerListI++) {
			if (readByte(buf) == 0) { 
				this.playerList.add(null);
			} else {
				BattlePlayerBean battlePlayerBean = new BattlePlayerBean();
				battlePlayerBean.read(buf);
				this.playerList.add(battlePlayerBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.groupId.size());
		for (int groupIdI = 0; groupIdI < this.groupId.size(); groupIdI++) {
			this.writeLong(buf, this.groupId.get(groupIdI));
		}
		this.writeInt(buf, hostId, false);
		this.writeInt(buf, matchType, false);
		this.writeString(buf, ip);
		this.writeInt(buf, port, false);
		this.writeInt(buf, mapId, false);
		this.writeInt(buf, cfgId, false);
		writeShort(buf, this.playerList.size());
		for (int playerListI = 0; playerListI < this.playerList.size(); playerListI++) {
			this.writeBean(buf, this.playerList.get(playerListI));
		}
		return true;
	}
}
