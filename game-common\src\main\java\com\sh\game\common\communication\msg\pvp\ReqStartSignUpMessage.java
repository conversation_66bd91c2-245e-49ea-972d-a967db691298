package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求报名跨服帮战</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqStartSignUpMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125014;
	}
	
	/**
	 * 帮会id
	 */
	private long uId;
	/**
	 * 帮会昵称
	 */
	private String unionName;
	/**
	 * 匹配组
	 */
	private int matchGroup;
	/**
	 * 前10名玩家总战力
	 */
	private long totalPower;
	/**
	 * 匹配类型
	 */
	private int matchType;
	/**
	 * 帮会积分
	 */
	private int unionScore;
	/**
	 * 区服名称
	 */
	private String serverName;
	/**
	 * 帮主名称
	 */
	private String leaderName;
	/**
	 * 帮主id
	 */
	private long leaderId;
	/**
	 * 帮派等级
	 */
	private int unionLevel;
	/**
	 * 副帮主id,副帮主报名才有值
	 */
	private long startUpLeader;
	/**
	 * 报名者创角天数
	 */
	private int playerCreateDays;

	public long getUId() {
		return uId;
	}

	public void setUId(long uId) {
		this.uId = uId;
	}

		public String getUnionName() {
		return unionName;
	}

	public void setUnionName(String unionName) {
		this.unionName = unionName;
	}

		public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

		public long getTotalPower() {
		return totalPower;
	}

	public void setTotalPower(long totalPower) {
		this.totalPower = totalPower;
	}

		public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

		public int getUnionScore() {
		return unionScore;
	}

	public void setUnionScore(int unionScore) {
		this.unionScore = unionScore;
	}

		public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

		public String getLeaderName() {
		return leaderName;
	}

	public void setLeaderName(String leaderName) {
		this.leaderName = leaderName;
	}

		public long getLeaderId() {
		return leaderId;
	}

	public void setLeaderId(long leaderId) {
		this.leaderId = leaderId;
	}

		public int getUnionLevel() {
		return unionLevel;
	}

	public void setUnionLevel(int unionLevel) {
		this.unionLevel = unionLevel;
	}

		public long getStartUpLeader() {
		return startUpLeader;
	}

	public void setStartUpLeader(long startUpLeader) {
		this.startUpLeader = startUpLeader;
	}

		public int getPlayerCreateDays() {
		return playerCreateDays;
	}

	public void setPlayerCreateDays(int playerCreateDays) {
		this.playerCreateDays = playerCreateDays;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.uId = readLong(buf);
		this.unionName = readString(buf);
		this.matchGroup = readInt(buf, false);
		this.totalPower = readLong(buf);
		this.matchType = readInt(buf, false);
		this.unionScore = readInt(buf, false);
		this.serverName = readString(buf);
		this.leaderName = readString(buf);
		this.leaderId = readLong(buf);
		this.unionLevel = readInt(buf, false);
		this.startUpLeader = readLong(buf);
		this.playerCreateDays = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, uId);
		this.writeString(buf, unionName);
		this.writeInt(buf, matchGroup, false);
		this.writeLong(buf, totalPower);
		this.writeInt(buf, matchType, false);
		this.writeInt(buf, unionScore, false);
		this.writeString(buf, serverName);
		this.writeString(buf, leaderName);
		this.writeLong(buf, leaderId);
		this.writeInt(buf, unionLevel, false);
		this.writeLong(buf, startUpLeader);
		this.writeInt(buf, playerCreateDays, false);
		return true;
	}
}
