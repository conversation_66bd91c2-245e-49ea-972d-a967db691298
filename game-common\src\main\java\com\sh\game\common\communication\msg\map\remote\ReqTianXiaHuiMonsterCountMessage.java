package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求天下会怪物数量</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toScene")
public class ReqTianXiaHuiMonsterCountMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 71023;
	}
	
	/**
	 * 怪物类型
	 */
	private int monsterType;

	public int getMonsterType() {
		return monsterType;
	}

	public void setMonsterType(int monsterType) {
		this.monsterType = monsterType;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.monsterType = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, monsterType, false);
		return true;
	}
}
