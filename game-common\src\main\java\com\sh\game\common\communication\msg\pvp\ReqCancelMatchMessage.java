package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>请求取消比赛</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqCancelMatchMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125012;
	}
	
	/**
	 * 组id
	 */
	private long groupId;
	/**
	 * 匹配类型
	 */
	private int matchType;

	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

		public int getMatchType() {
		return matchType;
	}

	public void setMatchType(int matchType) {
		this.matchType = matchType;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.groupId = readLong(buf);
		this.matchType = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, groupId);
		this.writeInt(buf, matchType, false);
		return true;
	}
}
