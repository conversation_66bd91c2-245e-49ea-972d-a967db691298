package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class RoleSimpleBean extends KryoBean {

	/**
	 * 角色id
	 */
	private long rid;
	/**
	 * 名字
	 */
	private String name;
	/**
	 * 等级
	 */
	private int level;
	/**
	 * 职业
	 */
	private int career;
	/**
	 * 性别
	 */
	private int sex;
	/**
	 * 头发
	 */
	private int hair;
	/**
	 * 帮会名字
	 */
	private String unionName;
	/**
	 * 装备
	 */
	private List<CommonEquipBean> equips = new ArrayList<>();
	/**
	 * 时装
	 */
	private List<CommonSlotBean> fashions = new ArrayList<>();
	/**
	 * 元婴
	 */
	private List<CommonYuanYingBean> yuanYings = new ArrayList<>();
	/**
	 * 注灵, key: 部位, value: 注灵配置id
	 */
	private List<CommonKeyValueBean> injectSoulList = new ArrayList<>();
	/**
	 * 兽决装备
	 */
	private List<CommonEquipBean> shouJueEquips = new ArrayList<>();
	/**
	 * 宠物id
	 */
	private int chongWuCfgID;
	/**
	 * 秘籍宝石信息
	 */
	private List<EquipPosGemBean> miJiStoneBeans = new ArrayList<>();
	/**
	 * 已镶嵌的法宝信息
	 */
	private List<MagicWeaponBlendBean> weaponList = new ArrayList<>();
	/**
	 * 宠物装备列表
	 */
	private List<CommonEquipBean> chongWuEquips = new ArrayList<>();
	/**
	 * 宠物装备强化信息
	 */
	private List<RolePetQiangHuaBean> rolePetQiangHuaBean = new ArrayList<>();
	/**
	 * 神器改造,key:部位,value:等级
	 */
	private List<CommonKeyValueBean> shenBingRemouldList = new ArrayList<>();
	/**
	 * 境界强化列表,key:pos部位,value:level等级
	 */
	private List<CommonKeyValueBean> realmIntensifyList = new ArrayList<>();
	/**
	 * 以购买的真言礼包信息
	 */
	private List<Integer> zhenYanGiftList = new ArrayList<>();
	/**
	 * 签名信息
	 */
	private List<QianMingBean> qianMingList = new ArrayList<>();

	public long getRid() {
		return rid;
	}

	public void setRid(long rid) {
		this.rid = rid;
	}

		public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

		public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}

		public int getCareer() {
		return career;
	}

	public void setCareer(int career) {
		this.career = career;
	}

		public int getSex() {
		return sex;
	}

	public void setSex(int sex) {
		this.sex = sex;
	}

		public int getHair() {
		return hair;
	}

	public void setHair(int hair) {
		this.hair = hair;
	}

		public String getUnionName() {
		return unionName;
	}

	public void setUnionName(String unionName) {
		this.unionName = unionName;
	}

		public List<CommonEquipBean> getEquips() {
		return equips;
	}

	public void setEquips(List<CommonEquipBean> equips) {
		this.equips = equips;
	}
	public List<CommonSlotBean> getFashions() {
		return fashions;
	}

	public void setFashions(List<CommonSlotBean> fashions) {
		this.fashions = fashions;
	}
	public List<CommonYuanYingBean> getYuanYings() {
		return yuanYings;
	}

	public void setYuanYings(List<CommonYuanYingBean> yuanYings) {
		this.yuanYings = yuanYings;
	}
	public List<CommonKeyValueBean> getInjectSoulList() {
		return injectSoulList;
	}

	public void setInjectSoulList(List<CommonKeyValueBean> injectSoulList) {
		this.injectSoulList = injectSoulList;
	}
	public List<CommonEquipBean> getShouJueEquips() {
		return shouJueEquips;
	}

	public void setShouJueEquips(List<CommonEquipBean> shouJueEquips) {
		this.shouJueEquips = shouJueEquips;
	}
	public int getChongWuCfgID() {
		return chongWuCfgID;
	}

	public void setChongWuCfgID(int chongWuCfgID) {
		this.chongWuCfgID = chongWuCfgID;
	}

		public List<EquipPosGemBean> getMiJiStoneBeans() {
		return miJiStoneBeans;
	}

	public void setMiJiStoneBeans(List<EquipPosGemBean> miJiStoneBeans) {
		this.miJiStoneBeans = miJiStoneBeans;
	}
	public List<MagicWeaponBlendBean> getWeaponList() {
		return weaponList;
	}

	public void setWeaponList(List<MagicWeaponBlendBean> weaponList) {
		this.weaponList = weaponList;
	}
	public List<CommonEquipBean> getChongWuEquips() {
		return chongWuEquips;
	}

	public void setChongWuEquips(List<CommonEquipBean> chongWuEquips) {
		this.chongWuEquips = chongWuEquips;
	}
	public List<RolePetQiangHuaBean> getRolePetQiangHuaBean() {
		return rolePetQiangHuaBean;
	}

	public void setRolePetQiangHuaBean(List<RolePetQiangHuaBean> rolePetQiangHuaBean) {
		this.rolePetQiangHuaBean = rolePetQiangHuaBean;
	}
	public List<CommonKeyValueBean> getShenBingRemouldList() {
		return shenBingRemouldList;
	}

	public void setShenBingRemouldList(List<CommonKeyValueBean> shenBingRemouldList) {
		this.shenBingRemouldList = shenBingRemouldList;
	}
	public List<CommonKeyValueBean> getRealmIntensifyList() {
		return realmIntensifyList;
	}

	public void setRealmIntensifyList(List<CommonKeyValueBean> realmIntensifyList) {
		this.realmIntensifyList = realmIntensifyList;
	}
	public List<Integer> getZhenYanGiftList() {
		return zhenYanGiftList;
	}

	public void setZhenYanGiftList(List<Integer> zhenYanGiftList) {
		this.zhenYanGiftList = zhenYanGiftList;
	}
	public List<QianMingBean> getQianMingList() {
		return qianMingList;
	}

	public void setQianMingList(List<QianMingBean> qianMingList) {
		this.qianMingList = qianMingList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.rid = readLong(buf);
		this.name = readString(buf);
		this.level = readInt(buf, false);
		this.career = readInt(buf, false);
		this.sex = readInt(buf, false);
		this.hair = readInt(buf, false);
		this.unionName = readString(buf);
		int equipsLength = readShort(buf);
		for (int equipsI = 0; equipsI < equipsLength; equipsI++) {
			if (readByte(buf) == 0) { 
				this.equips.add(null);
			} else {
				CommonEquipBean commonEquipBean = new CommonEquipBean();
				commonEquipBean.read(buf);
				this.equips.add(commonEquipBean);
			}
		}
		int fashionsLength = readShort(buf);
		for (int fashionsI = 0; fashionsI < fashionsLength; fashionsI++) {
			if (readByte(buf) == 0) { 
				this.fashions.add(null);
			} else {
				CommonSlotBean commonSlotBean = new CommonSlotBean();
				commonSlotBean.read(buf);
				this.fashions.add(commonSlotBean);
			}
		}
		int yuanYingsLength = readShort(buf);
		for (int yuanYingsI = 0; yuanYingsI < yuanYingsLength; yuanYingsI++) {
			if (readByte(buf) == 0) { 
				this.yuanYings.add(null);
			} else {
				CommonYuanYingBean commonYuanYingBean = new CommonYuanYingBean();
				commonYuanYingBean.read(buf);
				this.yuanYings.add(commonYuanYingBean);
			}
		}
		int injectSoulListLength = readShort(buf);
		for (int injectSoulListI = 0; injectSoulListI < injectSoulListLength; injectSoulListI++) {
			if (readByte(buf) == 0) { 
				this.injectSoulList.add(null);
			} else {
				CommonKeyValueBean commonKeyValueBean = new CommonKeyValueBean();
				commonKeyValueBean.read(buf);
				this.injectSoulList.add(commonKeyValueBean);
			}
		}
		int shouJueEquipsLength = readShort(buf);
		for (int shouJueEquipsI = 0; shouJueEquipsI < shouJueEquipsLength; shouJueEquipsI++) {
			if (readByte(buf) == 0) { 
				this.shouJueEquips.add(null);
			} else {
				CommonEquipBean commonEquipBean = new CommonEquipBean();
				commonEquipBean.read(buf);
				this.shouJueEquips.add(commonEquipBean);
			}
		}
		this.chongWuCfgID = readInt(buf, false);
		int miJiStoneBeansLength = readShort(buf);
		for (int miJiStoneBeansI = 0; miJiStoneBeansI < miJiStoneBeansLength; miJiStoneBeansI++) {
			if (readByte(buf) == 0) { 
				this.miJiStoneBeans.add(null);
			} else {
				EquipPosGemBean equipPosGemBean = new EquipPosGemBean();
				equipPosGemBean.read(buf);
				this.miJiStoneBeans.add(equipPosGemBean);
			}
		}
		int weaponListLength = readShort(buf);
		for (int weaponListI = 0; weaponListI < weaponListLength; weaponListI++) {
			if (readByte(buf) == 0) { 
				this.weaponList.add(null);
			} else {
				MagicWeaponBlendBean magicWeaponBlendBean = new MagicWeaponBlendBean();
				magicWeaponBlendBean.read(buf);
				this.weaponList.add(magicWeaponBlendBean);
			}
		}
		int chongWuEquipsLength = readShort(buf);
		for (int chongWuEquipsI = 0; chongWuEquipsI < chongWuEquipsLength; chongWuEquipsI++) {
			if (readByte(buf) == 0) { 
				this.chongWuEquips.add(null);
			} else {
				CommonEquipBean commonEquipBean = new CommonEquipBean();
				commonEquipBean.read(buf);
				this.chongWuEquips.add(commonEquipBean);
			}
		}
		int rolePetQiangHuaBeanLength = readShort(buf);
		for (int rolePetQiangHuaBeanI = 0; rolePetQiangHuaBeanI < rolePetQiangHuaBeanLength; rolePetQiangHuaBeanI++) {
			if (readByte(buf) == 0) { 
				this.rolePetQiangHuaBean.add(null);
			} else {
				RolePetQiangHuaBean rolePetQiangHuaBean = new RolePetQiangHuaBean();
				rolePetQiangHuaBean.read(buf);
				this.rolePetQiangHuaBean.add(rolePetQiangHuaBean);
			}
		}
		int shenBingRemouldListLength = readShort(buf);
		for (int shenBingRemouldListI = 0; shenBingRemouldListI < shenBingRemouldListLength; shenBingRemouldListI++) {
			if (readByte(buf) == 0) { 
				this.shenBingRemouldList.add(null);
			} else {
				CommonKeyValueBean commonKeyValueBean = new CommonKeyValueBean();
				commonKeyValueBean.read(buf);
				this.shenBingRemouldList.add(commonKeyValueBean);
			}
		}
		int realmIntensifyListLength = readShort(buf);
		for (int realmIntensifyListI = 0; realmIntensifyListI < realmIntensifyListLength; realmIntensifyListI++) {
			if (readByte(buf) == 0) { 
				this.realmIntensifyList.add(null);
			} else {
				CommonKeyValueBean commonKeyValueBean = new CommonKeyValueBean();
				commonKeyValueBean.read(buf);
				this.realmIntensifyList.add(commonKeyValueBean);
			}
		}
		int zhenYanGiftListLength = readShort(buf);
		for (int zhenYanGiftListI = 0; zhenYanGiftListI < zhenYanGiftListLength; zhenYanGiftListI++) {
			this.zhenYanGiftList.add(this.readInt(buf, false));
		}
		int qianMingListLength = readShort(buf);
		for (int qianMingListI = 0; qianMingListI < qianMingListLength; qianMingListI++) {
			if (readByte(buf) == 0) { 
				this.qianMingList.add(null);
			} else {
				QianMingBean qianMingBean = new QianMingBean();
				qianMingBean.read(buf);
				this.qianMingList.add(qianMingBean);
			}
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, rid);
		this.writeString(buf, name);
		this.writeInt(buf, level, false);
		this.writeInt(buf, career, false);
		this.writeInt(buf, sex, false);
		this.writeInt(buf, hair, false);
		this.writeString(buf, unionName);
		writeShort(buf, this.equips.size());
		for (int equipsI = 0; equipsI < this.equips.size(); equipsI++) {
			this.writeBean(buf, this.equips.get(equipsI));
		}
		writeShort(buf, this.fashions.size());
		for (int fashionsI = 0; fashionsI < this.fashions.size(); fashionsI++) {
			this.writeBean(buf, this.fashions.get(fashionsI));
		}
		writeShort(buf, this.yuanYings.size());
		for (int yuanYingsI = 0; yuanYingsI < this.yuanYings.size(); yuanYingsI++) {
			this.writeBean(buf, this.yuanYings.get(yuanYingsI));
		}
		writeShort(buf, this.injectSoulList.size());
		for (int injectSoulListI = 0; injectSoulListI < this.injectSoulList.size(); injectSoulListI++) {
			this.writeBean(buf, this.injectSoulList.get(injectSoulListI));
		}
		writeShort(buf, this.shouJueEquips.size());
		for (int shouJueEquipsI = 0; shouJueEquipsI < this.shouJueEquips.size(); shouJueEquipsI++) {
			this.writeBean(buf, this.shouJueEquips.get(shouJueEquipsI));
		}
		this.writeInt(buf, chongWuCfgID, false);
		writeShort(buf, this.miJiStoneBeans.size());
		for (int miJiStoneBeansI = 0; miJiStoneBeansI < this.miJiStoneBeans.size(); miJiStoneBeansI++) {
			this.writeBean(buf, this.miJiStoneBeans.get(miJiStoneBeansI));
		}
		writeShort(buf, this.weaponList.size());
		for (int weaponListI = 0; weaponListI < this.weaponList.size(); weaponListI++) {
			this.writeBean(buf, this.weaponList.get(weaponListI));
		}
		writeShort(buf, this.chongWuEquips.size());
		for (int chongWuEquipsI = 0; chongWuEquipsI < this.chongWuEquips.size(); chongWuEquipsI++) {
			this.writeBean(buf, this.chongWuEquips.get(chongWuEquipsI));
		}
		writeShort(buf, this.rolePetQiangHuaBean.size());
		for (int rolePetQiangHuaBeanI = 0; rolePetQiangHuaBeanI < this.rolePetQiangHuaBean.size(); rolePetQiangHuaBeanI++) {
			this.writeBean(buf, this.rolePetQiangHuaBean.get(rolePetQiangHuaBeanI));
		}
		writeShort(buf, this.shenBingRemouldList.size());
		for (int shenBingRemouldListI = 0; shenBingRemouldListI < this.shenBingRemouldList.size(); shenBingRemouldListI++) {
			this.writeBean(buf, this.shenBingRemouldList.get(shenBingRemouldListI));
		}
		writeShort(buf, this.realmIntensifyList.size());
		for (int realmIntensifyListI = 0; realmIntensifyListI < this.realmIntensifyList.size(); realmIntensifyListI++) {
			this.writeBean(buf, this.realmIntensifyList.get(realmIntensifyListI));
		}
		writeShort(buf, this.zhenYanGiftList.size());
		for (int zhenYanGiftListI = 0; zhenYanGiftListI < this.zhenYanGiftList.size(); zhenYanGiftListI++) {
			this.writeInt(buf, this.zhenYanGiftList.get(zhenYanGiftListI), false);
		}
		writeShort(buf, this.qianMingList.size());
		for (int qianMingListI = 0; qianMingListI < this.qianMingList.size(); qianMingListI++) {
			this.writeBean(buf, this.qianMingList.get(qianMingListI));
		}
		return true;
	}
}
