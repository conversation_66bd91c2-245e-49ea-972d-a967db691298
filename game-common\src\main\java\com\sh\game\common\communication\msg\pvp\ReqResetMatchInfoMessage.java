package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.UnionStartUpBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>群雄逐鹿->游戏服断线重连匹配服重置数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqResetMatchInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125022;
	}
	
	/**
	 * 匹配组
	 */
	private int matchGroup;
	/**
	 * 服务器名称
	 */
	private String serverName;
	/**
	 * 匹配帮会列表
	 */
	private List<UnionStartUpBean> unionStartUpBean = new ArrayList<>();

	public int getMatchGroup() {
		return matchGroup;
	}

	public void setMatchGroup(int matchGroup) {
		this.matchGroup = matchGroup;
	}

		public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

		public List<UnionStartUpBean> getUnionStartUpBean() {
		return unionStartUpBean;
	}

	public void setUnionStartUpBean(List<UnionStartUpBean> unionStartUpBean) {
		this.unionStartUpBean = unionStartUpBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.matchGroup = readInt(buf, false);
		this.serverName = readString(buf);
		int unionStartUpBeanLength = readShort(buf);
		for (int unionStartUpBeanI = 0; unionStartUpBeanI < unionStartUpBeanLength; unionStartUpBeanI++) {
			if (readByte(buf) == 0) { 
				this.unionStartUpBean.add(null);
			} else {
				UnionStartUpBean unionStartUpBean = new UnionStartUpBean();
				unionStartUpBean.read(buf);
				this.unionStartUpBean.add(unionStartUpBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, matchGroup, false);
		this.writeString(buf, serverName);
		writeShort(buf, this.unionStartUpBean.size());
		for (int unionStartUpBeanI = 0; unionStartUpBeanI < this.unionStartUpBean.size(); unionStartUpBeanI++) {
			this.writeBean(buf, this.unionStartUpBean.get(unionStartUpBeanI));
		}
		return true;
	}
}
