package com.sh.game.back.client;

import com.sh.client.*;
import com.sh.game.common.communication.msg.system.back.ReqReloadCfgMessage;
import com.sh.game.common.communication.msg.system.back.ResBackLoginMessage;
import com.sh.game.common.communication.msg.system.back.ResBackRetMessage;
import com.sh.game.common.util.StatUtil;
import com.sh.game.option.ServerOption;
import com.sh.game.protos.BackProtos;


/**
 * 重新加载配置文件客户端
 */
public class ReloadCfgClient {

    private static final int TYPE_ALL = 1;

    private static final int TYPE_CONFIG = 2;

    private static final int TYPE_CACHE = 3;

    public static void main(String[] args) {

        try {


            if (args.length < 2) {
                System.out.println("参数不足");
                return;
            }

            int type = Integer.parseInt(args[1]);
            if (type != TYPE_ALL && type != TYPE_CONFIG && type != TYPE_CACHE) {
                System.out.println("错误的刷新类型");
                return;
            }


            String cfgOrCacheName = null;

            if (type != TYPE_ALL) {
                if (args.length < 3) {
                    System.out.println("参数不足");
                    return;
                }

                cfgOrCacheName = args[2];
            }


            ClientBuilder builder = new ClientBuilder();

            String optionPath = args[0];

            ServerOption option = new ServerOption();
            option.build(optionPath);


            builder.setHost("localhost");
            builder.setPort(option.getBackServerPort());
            builder.setEventlistener(new NetworkEventlistenerAdapter());
            builder.setConsumer(new NetworkConsumerAdapter());
            DefaultMessagePool pool = new DefaultMessagePool();
            pool.register(new ResBackRetMessage(), null);
            pool.register(new ResBackLoginMessage(), null);
            builder.setMsgPool(pool);
            builder.setNeedReconnect(false);
            Client client = builder.createClient();
            client.connect(true);


            if(!StatUtil.waitInit(client))
                return;


            if(!StatUtil.login(client, option.getBackLoginSign()))
                return;

            ReqReloadCfgMessage req = new ReqReloadCfgMessage();
            BackProtos.ReqReloadCfg.Builder reloadCfg = BackProtos.ReqReloadCfg.newBuilder();

            reloadCfg.setType(type);
            if (type == TYPE_CONFIG) {
                reloadCfg.setCfgName(cfgOrCacheName == null ? "" : cfgOrCacheName);
            } else if (type == TYPE_CACHE) {
                reloadCfg.setCacheName(cfgOrCacheName == null ? "" : cfgOrCacheName);
            }

            req.setProto(reloadCfg.build());
            ResBackRetMessage res = (ResBackRetMessage) client.sendSyncMsg(req, 10_000);

            if (res == null) {
                System.out.println("请求失败");
                System.exit(0);
            } else {
                System.out.println(res.getProto().getRet());
            }
            client.stopQuickly();
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            System.exit(0);
        }
    }


}
