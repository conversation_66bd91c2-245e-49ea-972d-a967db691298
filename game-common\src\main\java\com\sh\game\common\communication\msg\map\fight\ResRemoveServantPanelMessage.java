package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回移除召唤宠物面板</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRemoveServantPanelMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ResRemoveServantPanel proto;

    private com.sh.game.protos.FightProtos.ResRemoveServantPanel.Builder builder;

	
	@Override
	public int getId() {
		return 69016;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ResRemoveServantPanel.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ResRemoveServantPanel.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ResRemoveServantPanel.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ResRemoveServantPanel getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ResRemoveServantPanel proto) {
        this.proto = proto;
    }

}
