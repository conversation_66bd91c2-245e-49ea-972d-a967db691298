package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>地图停留时间更新</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResMapUpdateKeepTimeMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResMapUpdateKeepTime proto;

    private com.sh.game.protos.MapProtos.ResMapUpdateKeepTime.Builder builder;

	
	@Override
	public int getId() {
		return 67085;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResMapUpdateKeepTime.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResMapUpdateKeepTime.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResMapUpdateKeepTime.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResMapUpdateKeepTime getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResMapUpdateKeepTime proto) {
        this.proto = proto;
    }

}
