package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonEquipBean extends KryoBean {

	/**
	 * 
	 */
	private CommonItemBean item;
	/**
	 * 装备位置
	 */
	private int index;

	public CommonItemBean getItem() {
		return item;
	}

	public void setItem(CommonItemBean item) {
		this.item = item;
	}

		public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		if (readByte(buf) != 0) {
			CommonItemBean commonItemBean = new CommonItemBean();
			commonItemBean.read(buf);
			this.item = commonItemBean;
		}
		this.index = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeBean(buf, item);
		this.writeInt(buf, index, false);
		return true;
	}
}
