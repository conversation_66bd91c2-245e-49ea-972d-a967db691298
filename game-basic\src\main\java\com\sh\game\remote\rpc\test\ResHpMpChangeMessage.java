package com.sh.game.remote.rpc.test;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>血量蓝量发生变化（用于单独的通知血量，比如说buffer伤害等等）</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResHpMpChangeMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 69004;
	}
	
	/**
	 * id
	 */
	private long lid;
	/**
	 * 当前血量
	 */
	private long hp;
	/**
	 * 当前蓝量
	 */
	private int mp;
	/**
	 * 飘字类型
	 */
	private int type;

	public long getLid() {
		return lid;
	}

	public void setLid(long lid) {
		this.lid = lid;
	}

		public long getHp() {
		return hp;
	}

	public void setHp(long hp) {
		this.hp = hp;
	}

		public int getMp() {
		return mp;
	}

	public void setMp(int mp) {
		this.mp = mp;
	}

		public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.lid = readLong(buf);
		this.hp = readLong(buf);
		this.mp = readInt(buf, false);
		this.type = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, lid);
		this.writeLong(buf, hp);
		this.writeInt(buf, mp, false);
		this.writeInt(buf, type, false);
		return true;
	}
}
