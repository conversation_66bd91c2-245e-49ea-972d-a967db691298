package com.sh.game.common.communication.msg.map.move;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求慢走</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqWalkMessage extends ProtobufMessage {

    private com.sh.game.protos.MoveProtos.ReqWalk proto;

    private com.sh.game.protos.MoveProtos.ReqWalk.Builder builder;

	
	@Override
	public int getId() {
		return 68001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MoveProtos.ReqWalk.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MoveProtos.ReqWalk.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MoveProtos.ReqWalk.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MoveProtos.ReqWalk getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MoveProtos.ReqWalk proto) {
        this.proto = proto;
    }

}
