package com.sh.game.remote.rpc;

import com.sh.game.notice.ProcessNotice;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.net.Message;
import com.sh.server.Session;
import io.netty.channel.Channel;
import lombok.Getter;
import lombok.Setter;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * ModuleClient
 *
 * <AUTHOR>
 * @date 2020/8/26 14:54
 */
@Getter
@Setter
public class RPCConnection {

    /**
     * 最大心跳间隔时间，超过这个心跳，就算掉线
     */
    private static final int MAX_HEART_DT = 20000;

    private int hostId;

    private int port;

    private String host;

    private Channel channel;

    private int[] reconnectDelays = {2, 2};

    private int activeIndex;

    private int nextActiveIndex;

    private boolean destroy;

    private ModuleClient client;

    private AtomicInteger login = new AtomicInteger();

    private int type;

    private boolean running = false;


    public boolean checkHeart(long curTime) {
        if (channel == null) {
            return false;
        }
        Session activeSession = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
        ModuleSessionValue value = (ModuleSessionValue) activeSession.getValue();
        long lastHeartTime = value.getHeart();
        if (lastHeartTime == 0) {
            lastHeartTime = curTime;
            value.setHeart(lastHeartTime);
        }
        long dt = curTime - lastHeartTime;
        if (dt > MAX_HEART_DT) {
            value.setHeart(0);
            channel.close();
            return false;
        }
        return true;
    }

    public void changeActive(int activeIndex) {
        this.activeIndex = activeIndex;
    }


    //以下只有客户端才会用到
    public void resetReconnectDelay(int index) {
        this.getReconnectDelays()[index] = 2;
    }

    public int getReconnectDelay(int index) {
        int ret = this.getReconnectDelays()[index];
        //计算下次重连的延迟时间
        int reconnectDelay = this.getReconnectDelays()[index];
        reconnectDelay = reconnectDelay << 1;
        if (reconnectDelay > 20) {
            reconnectDelay = 20;
        }
        this.getReconnectDelays()[index] = reconnectDelay;
        return ret;
    }

    public boolean available() {
        return this.channel != null && this.channel.isActive();
    }

    public void close() {
        this.channel.close();
    }

    public void setChannel(Channel channel) {
        this.channel = channel;
    }

    public Channel activeChannel() {
        return this.channel;
    }

    public void transformMessage(Message message, long id) {
        MessageTransform transform = new MessageTransform();
        transform.getPidList().add(id);
        transform.setMessageId(message.getId());
        transform.setMessage(message);
        transform.encodeTransform();
        activeChannel().writeAndFlush(transform);
    }

    public void transformMessage(Message message, Collection<Long> idList) {
        MessageTransform transform = new MessageTransform();
        transform.getPidList().addAll(idList);
        transform.setMessageId(message.getId());
        transform.setMessage(message);
        transform.encodeTransform();
        activeChannel().writeAndFlush(transform);
    }

    public void transformNotice(ProcessNotice notice, int processorId, long id) {
        NoticeTransform transform = new NoticeTransform();
        transform.setNoticeId(notice.getNoticeID());
        transform.setNotice(notice);
        transform.setPlayerId(id);
        transform.setProcessId((byte) processorId);
        transform.encodeTransform();
        activeChannel().writeAndFlush(transform);
    }


    public String toString() {
        return "[" + host + ":" + port + "/" + hostId + "]";
    }
}
