package com.sh.game.common.communication.msg.map.remote;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回消费排行结束结算信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResConsumeRankEndAwardMessage extends ProtobufMessage {

    private com.sh.game.protos.RpcProtos.ResConsumeRankEndAward proto;

    private com.sh.game.protos.RpcProtos.ResConsumeRankEndAward.Builder builder;

	
	@Override
	public int getId() {
		return 82019;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RpcProtos.ResConsumeRankEndAward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RpcProtos.ResConsumeRankEndAward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RpcProtos.ResConsumeRankEndAward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RpcProtos.ResConsumeRankEndAward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RpcProtos.ResConsumeRankEndAward proto) {
        this.proto = proto;
    }

}
