package com.sh.game.remote.rpc.msg;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * ConnectionCompleteMessage
 *
 * <AUTHOR>
 * @date 2020/8/28 11:18
 */
public class ClientConnectCompleteMessage extends AbstractMessage {

    private int moduleId;

    private int activeIndex;

    @Override
    public int getId() {
        return 18;
    }


    @Override
    public boolean read(KryoInput buf) {
        this.moduleId = this.readInt(buf, false);
        this.activeIndex = this.readInt(buf, false);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {
        this.writeInt(buf, this.moduleId, false);
        this.writeInt(buf, this.activeIndex, false);
        return true;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public int getActiveIndex() {
        return activeIndex;
    }

    public void setActiveIndex(int activeIndex) {
        this.activeIndex = activeIndex;
    }
}
