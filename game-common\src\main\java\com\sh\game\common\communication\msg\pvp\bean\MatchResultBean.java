package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class MatchResultBean extends KryoBean {

	/**
	 * 帮会昵称
	 */
	private String unionName;
	/**
	 * 帮主昵称
	 */
	private String leaderName;
	/**
	 * 帮会所在服务器昵称
	 */
	private String serverName;

	public String getUnionName() {
		return unionName;
	}

	public void setUnionName(String unionName) {
		this.unionName = unionName;
	}

		public String getLeaderName() {
		return leaderName;
	}

	public void setLeaderName(String leaderName) {
		this.leaderName = leaderName;
	}

		public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.unionName = readString(buf);
		this.leaderName = readString(buf);
		this.serverName = readString(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeString(buf, unionName);
		this.writeString(buf, leaderName);
		this.writeString(buf, serverName);
		return true;
	}
}
