package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class EquipDataBean extends KryoBean {

	/**
	 * 消耗耐久
	 */
	private int durable;
	/**
	 * 升星等级
	 */
	private int starLevel;
	/**
	 * 被锁星级
	 */
	private int starLock;
	/**
	 * 升星祝福值
	 */
	private int starExp;
	/**
	 * 觉醒等级
	 */
	private int awakeLevel;
	/**
	 * 极品属性
	 */
	private List<RandAttributeBean> superAttrs = new ArrayList<>();
	/**
	 * 鉴定属性
	 */
	private List<IdentifyAttributeBean> identifyAttrs = new ArrayList<>();
	/**
	 * 鉴定Buff
	 */
	private List<IdentifyAttributeBean> identifyBuffs = new ArrayList<>();
	/**
	 * 是否被封印了，1表示封印
	 */
	private int fengyin;
	/**
	 * 祝福等级
	 */
	private int zhufu;
	/**
	 * 融合的特戒id
	 */
	private int tjId;
	/**
	 * 祝福属性
	 */
	private List<RandAttributeBean> zhufuAttrs = new ArrayList<>();
	/**
	 * 魔血石剩余值
	 */
	private int magicBloodStoneValue;
	/**
	 * 洗练属性
	 */
	private List<RandAttributeBean> xilianAttrs = new ArrayList<>();
	/**
	 * 融合的魔器id
	 */
	private int demonEquipId;
	/**
	 * 宝石ID
	 */
	private int gemId;
	/**
	 * 对应的资质表id
	 */
	private int aptitudeCfgId;
	/**
	 * 资质装备的资质值
	 */
	private int aptitudeValue;
	/**
	 * 资质属性
	 */
	private List<RandAttributeBean> aptitudeAttrs = new ArrayList<>();
	/**
	 * 1投保，0未投保
	 */
	private int toubao;
	/**
	 * 装备继承的套装id
	 */
	private List<Integer> suitIdList = new ArrayList<>();
	/**
	 * 缔造属性
	 */
	private List<RandAttributeBean> diZaoAttribute = new ArrayList<>();
	/**
	 * 缔造属性万分比
	 */
	private int rate;
	/**
	 * cfg_destinyn_special的id
	 */
	private int xingMingId;
	/**
	 * 品阶
	 */
	private int rare;
	/**
	 * 品阶属性
	 */
	private List<RandAttributeBean> rareAttrs = new ArrayList<>();
	/**
	 * 法宝属性万分比
	 */
	private int magicWeaponRate;
	/**
	 * 镶嵌融合的法宝
	 */
	private List<MagicWeaponBlendBean> magicWeaponBlendList = new ArrayList<>();
	/**
	 * 法宝携带的buffId
	 */
	private int magicWeaponBuffId;
	/**
	 * 兽决强化等级
	 */
	private int shouJueLevel;
	/**
	 * 泰坦神石id
	 */
	private int taiTanGemId;
	/**
	 * 融合龙魂itemId列表
	 */
	private List<Integer> longhunGemIdList = new ArrayList<>();
	/**
	 * 淬炼等级
	 */
	private int refinementLevel;
	/**
	 * 缴械时间戳(ms)
	 */
	private long disarmTime;
	/**
	 * 淬炼额外成功率
	 */
	private int refinementRate;
	/**
	 * 宠物装备品质万分比
	 */
	private int petEquipRate;
	/**
	 * 宠物装备获得buff
	 */
	private int petBuff;
	/**
	 * 混沌宝石id
	 */
	private int chaoticGemId;
	/**
	 * 融合侠魂itemId列表
	 */
	private List<Integer> xiahunGemIdList = new ArrayList<>();
	/**
	 * 混沌赋灵等级
	 */
	private int chaoticPowerLevel;
	/**
	 * 神兵铸魂等级
	 */
	private int shenBingZhuHunLevel;
	/**
	 * 命格等级
	 */
	private int birthChartLevel;

	public int getDurable() {
		return durable;
	}

	public void setDurable(int durable) {
		this.durable = durable;
	}

	public int getStarLevel() {
		return starLevel;
	}

	public void setStarLevel(int starLevel) {
		this.starLevel = starLevel;
	}

	public int getStarLock() {
		return starLock;
	}

	public void setStarLock(int starLock) {
		this.starLock = starLock;
	}

	public int getStarExp() {
		return starExp;
	}

	public void setStarExp(int starExp) {
		this.starExp = starExp;
	}

	public int getAwakeLevel() {
		return awakeLevel;
	}

	public void setAwakeLevel(int awakeLevel) {
		this.awakeLevel = awakeLevel;
	}

	public List<RandAttributeBean> getSuperAttrs() {
		return superAttrs;
	}

	public void setSuperAttrs(List<RandAttributeBean> superAttrs) {
		this.superAttrs = superAttrs;
	}
	public List<IdentifyAttributeBean> getIdentifyAttrs() {
		return identifyAttrs;
	}

	public void setIdentifyAttrs(List<IdentifyAttributeBean> identifyAttrs) {
		this.identifyAttrs = identifyAttrs;
	}
	public List<IdentifyAttributeBean> getIdentifyBuffs() {
		return identifyBuffs;
	}

	public void setIdentifyBuffs(List<IdentifyAttributeBean> identifyBuffs) {
		this.identifyBuffs = identifyBuffs;
	}
	public int getFengyin() {
		return fengyin;
	}

	public void setFengyin(int fengyin) {
		this.fengyin = fengyin;
	}

	public int getZhufu() {
		return zhufu;
	}

	public void setZhufu(int zhufu) {
		this.zhufu = zhufu;
	}

	public int getTjId() {
		return tjId;
	}

	public void setTjId(int tjId) {
		this.tjId = tjId;
	}

	public List<RandAttributeBean> getZhufuAttrs() {
		return zhufuAttrs;
	}

	public void setZhufuAttrs(List<RandAttributeBean> zhufuAttrs) {
		this.zhufuAttrs = zhufuAttrs;
	}
	public int getMagicBloodStoneValue() {
		return magicBloodStoneValue;
	}

	public void setMagicBloodStoneValue(int magicBloodStoneValue) {
		this.magicBloodStoneValue = magicBloodStoneValue;
	}

	public List<RandAttributeBean> getXilianAttrs() {
		return xilianAttrs;
	}

	public void setXilianAttrs(List<RandAttributeBean> xilianAttrs) {
		this.xilianAttrs = xilianAttrs;
	}
	public int getDemonEquipId() {
		return demonEquipId;
	}

	public void setDemonEquipId(int demonEquipId) {
		this.demonEquipId = demonEquipId;
	}

	public int getGemId() {
		return gemId;
	}

	public void setGemId(int gemId) {
		this.gemId = gemId;
	}

	public int getAptitudeCfgId() {
		return aptitudeCfgId;
	}

	public void setAptitudeCfgId(int aptitudeCfgId) {
		this.aptitudeCfgId = aptitudeCfgId;
	}

	public int getAptitudeValue() {
		return aptitudeValue;
	}

	public void setAptitudeValue(int aptitudeValue) {
		this.aptitudeValue = aptitudeValue;
	}

	public List<RandAttributeBean> getAptitudeAttrs() {
		return aptitudeAttrs;
	}

	public void setAptitudeAttrs(List<RandAttributeBean> aptitudeAttrs) {
		this.aptitudeAttrs = aptitudeAttrs;
	}
	public int getToubao() {
		return toubao;
	}

	public void setToubao(int toubao) {
		this.toubao = toubao;
	}

	public List<Integer> getSuitIdList() {
		return suitIdList;
	}

	public void setSuitIdList(List<Integer> suitIdList) {
		this.suitIdList = suitIdList;
	}
	public List<RandAttributeBean> getDiZaoAttribute() {
		return diZaoAttribute;
	}

	public void setDiZaoAttribute(List<RandAttributeBean> diZaoAttribute) {
		this.diZaoAttribute = diZaoAttribute;
	}
	public int getRate() {
		return rate;
	}

	public void setRate(int rate) {
		this.rate = rate;
	}

	public int getXingMingId() {
		return xingMingId;
	}

	public void setXingMingId(int xingMingId) {
		this.xingMingId = xingMingId;
	}

	public int getRare() {
		return rare;
	}

	public void setRare(int rare) {
		this.rare = rare;
	}

	public List<RandAttributeBean> getRareAttrs() {
		return rareAttrs;
	}

	public void setRareAttrs(List<RandAttributeBean> rareAttrs) {
		this.rareAttrs = rareAttrs;
	}
	public int getMagicWeaponRate() {
		return magicWeaponRate;
	}

	public void setMagicWeaponRate(int magicWeaponRate) {
		this.magicWeaponRate = magicWeaponRate;
	}

	public List<MagicWeaponBlendBean> getMagicWeaponBlendList() {
		return magicWeaponBlendList;
	}

	public void setMagicWeaponBlendList(List<MagicWeaponBlendBean> magicWeaponBlendList) {
		this.magicWeaponBlendList = magicWeaponBlendList;
	}
	public int getMagicWeaponBuffId() {
		return magicWeaponBuffId;
	}

	public void setMagicWeaponBuffId(int magicWeaponBuffId) {
		this.magicWeaponBuffId = magicWeaponBuffId;
	}

	public int getShouJueLevel() {
		return shouJueLevel;
	}

	public void setShouJueLevel(int shouJueLevel) {
		this.shouJueLevel = shouJueLevel;
	}

	public int getTaiTanGemId() {
		return taiTanGemId;
	}

	public void setTaiTanGemId(int taiTanGemId) {
		this.taiTanGemId = taiTanGemId;
	}

	public List<Integer> getLonghunGemIdList() {
		return longhunGemIdList;
	}

	public void setLonghunGemIdList(List<Integer> longhunGemIdList) {
		this.longhunGemIdList = longhunGemIdList;
	}
	public int getRefinementLevel() {
		return refinementLevel;
	}

	public void setRefinementLevel(int refinementLevel) {
		this.refinementLevel = refinementLevel;
	}

	public long getDisarmTime() {
		return disarmTime;
	}

	public void setDisarmTime(long disarmTime) {
		this.disarmTime = disarmTime;
	}

	public int getRefinementRate() {
		return refinementRate;
	}

	public void setRefinementRate(int refinementRate) {
		this.refinementRate = refinementRate;
	}

	public int getPetEquipRate() {
		return petEquipRate;
	}

	public void setPetEquipRate(int petEquipRate) {
		this.petEquipRate = petEquipRate;
	}

	public int getPetBuff() {
		return petBuff;
	}

	public void setPetBuff(int petBuff) {
		this.petBuff = petBuff;
	}

	public int getChaoticGemId() {
		return chaoticGemId;
	}

	public void setChaoticGemId(int chaoticGemId) {
		this.chaoticGemId = chaoticGemId;
	}

	public List<Integer> getXiahunGemIdList() {
		return xiahunGemIdList;
	}

	public void setXiahunGemIdList(List<Integer> xiahunGemIdList) {
		this.xiahunGemIdList = xiahunGemIdList;
	}
	public int getChaoticPowerLevel() {
		return chaoticPowerLevel;
	}

	public void setChaoticPowerLevel(int chaoticPowerLevel) {
		this.chaoticPowerLevel = chaoticPowerLevel;
	}

	public int getShenBingZhuHunLevel() {
		return shenBingZhuHunLevel;
	}

	public void setShenBingZhuHunLevel(int shenBingZhuHunLevel) {
		this.shenBingZhuHunLevel = shenBingZhuHunLevel;
	}

	public int getBirthChartLevel() {
		return birthChartLevel;
	}

	public void setBirthChartLevel(int birthChartLevel) {
		this.birthChartLevel = birthChartLevel;
	}


	@Override
	public boolean read(KryoInput buf) {

		this.durable = readInt(buf, false);
		this.starLevel = readInt(buf, false);
		this.starLock = readInt(buf, false);
		this.starExp = readInt(buf, false);
		this.awakeLevel = readInt(buf, false);
		int superAttrsLength = readShort(buf);
		for (int superAttrsI = 0; superAttrsI < superAttrsLength; superAttrsI++) {
			if (readByte(buf) == 0) {
				this.superAttrs.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.superAttrs.add(randAttributeBean);
			}
		}
		int identifyAttrsLength = readShort(buf);
		for (int identifyAttrsI = 0; identifyAttrsI < identifyAttrsLength; identifyAttrsI++) {
			if (readByte(buf) == 0) {
				this.identifyAttrs.add(null);
			} else {
				IdentifyAttributeBean identifyAttributeBean = new IdentifyAttributeBean();
				identifyAttributeBean.read(buf);
				this.identifyAttrs.add(identifyAttributeBean);
			}
		}
		int identifyBuffsLength = readShort(buf);
		for (int identifyBuffsI = 0; identifyBuffsI < identifyBuffsLength; identifyBuffsI++) {
			if (readByte(buf) == 0) {
				this.identifyBuffs.add(null);
			} else {
				IdentifyAttributeBean identifyAttributeBean = new IdentifyAttributeBean();
				identifyAttributeBean.read(buf);
				this.identifyBuffs.add(identifyAttributeBean);
			}
		}
		this.fengyin = readInt(buf, false);
		this.zhufu = readInt(buf, false);
		this.tjId = readInt(buf, false);
		int zhufuAttrsLength = readShort(buf);
		for (int zhufuAttrsI = 0; zhufuAttrsI < zhufuAttrsLength; zhufuAttrsI++) {
			if (readByte(buf) == 0) {
				this.zhufuAttrs.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.zhufuAttrs.add(randAttributeBean);
			}
		}
		this.magicBloodStoneValue = readInt(buf, false);
		int xilianAttrsLength = readShort(buf);
		for (int xilianAttrsI = 0; xilianAttrsI < xilianAttrsLength; xilianAttrsI++) {
			if (readByte(buf) == 0) {
				this.xilianAttrs.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.xilianAttrs.add(randAttributeBean);
			}
		}
		this.demonEquipId = readInt(buf, false);
		this.gemId = readInt(buf, false);
		this.aptitudeCfgId = readInt(buf, false);
		this.aptitudeValue = readInt(buf, false);
		int aptitudeAttrsLength = readShort(buf);
		for (int aptitudeAttrsI = 0; aptitudeAttrsI < aptitudeAttrsLength; aptitudeAttrsI++) {
			if (readByte(buf) == 0) {
				this.aptitudeAttrs.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.aptitudeAttrs.add(randAttributeBean);
			}
		}
		this.toubao = readInt(buf, false);
		int suitIdListLength = readShort(buf);
		for (int suitIdListI = 0; suitIdListI < suitIdListLength; suitIdListI++) {
			this.suitIdList.add(this.readInt(buf, false));
		}
		int diZaoAttributeLength = readShort(buf);
		for (int diZaoAttributeI = 0; diZaoAttributeI < diZaoAttributeLength; diZaoAttributeI++) {
			if (readByte(buf) == 0) {
				this.diZaoAttribute.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.diZaoAttribute.add(randAttributeBean);
			}
		}
		this.rate = readInt(buf, false);
		this.xingMingId = readInt(buf, false);
		this.rare = readInt(buf, false);
		int rareAttrsLength = readShort(buf);
		for (int rareAttrsI = 0; rareAttrsI < rareAttrsLength; rareAttrsI++) {
			if (readByte(buf) == 0) {
				this.rareAttrs.add(null);
			} else {
				RandAttributeBean randAttributeBean = new RandAttributeBean();
				randAttributeBean.read(buf);
				this.rareAttrs.add(randAttributeBean);
			}
		}
		this.magicWeaponRate = readInt(buf, false);
		int magicWeaponBlendListLength = readShort(buf);
		for (int magicWeaponBlendListI = 0; magicWeaponBlendListI < magicWeaponBlendListLength; magicWeaponBlendListI++) {
			if (readByte(buf) == 0) {
				this.magicWeaponBlendList.add(null);
			} else {
				MagicWeaponBlendBean magicWeaponBlendBean = new MagicWeaponBlendBean();
				magicWeaponBlendBean.read(buf);
				this.magicWeaponBlendList.add(magicWeaponBlendBean);
			}
		}
		this.magicWeaponBuffId = readInt(buf, false);
		this.shouJueLevel = readInt(buf, false);
		this.taiTanGemId = readInt(buf, false);
		int longhunGemIdListLength = readShort(buf);
		for (int longhunGemIdListI = 0; longhunGemIdListI < longhunGemIdListLength; longhunGemIdListI++) {
			this.longhunGemIdList.add(this.readInt(buf, false));
		}
		this.refinementLevel = readInt(buf, false);
		this.disarmTime = readLong(buf);
		this.refinementRate = readInt(buf, false);
		this.petEquipRate = readInt(buf, false);
		this.petBuff = readInt(buf, false);
		this.chaoticGemId = readInt(buf, false);
		int xiahunGemIdListLength = readShort(buf);
		for (int xiahunGemIdListI = 0; xiahunGemIdListI < xiahunGemIdListLength; xiahunGemIdListI++) {
			this.xiahunGemIdList.add(this.readInt(buf, false));
		}
		this.chaoticPowerLevel = readInt(buf, false);
		this.shenBingZhuHunLevel = readInt(buf, false);
		this.birthChartLevel = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, durable, false);
		this.writeInt(buf, starLevel, false);
		this.writeInt(buf, starLock, false);
		this.writeInt(buf, starExp, false);
		this.writeInt(buf, awakeLevel, false);
		writeShort(buf, this.superAttrs.size());
		for (int superAttrsI = 0; superAttrsI < this.superAttrs.size(); superAttrsI++) {
			this.writeBean(buf, this.superAttrs.get(superAttrsI));
		}
		writeShort(buf, this.identifyAttrs.size());
		for (int identifyAttrsI = 0; identifyAttrsI < this.identifyAttrs.size(); identifyAttrsI++) {
			this.writeBean(buf, this.identifyAttrs.get(identifyAttrsI));
		}
		writeShort(buf, this.identifyBuffs.size());
		for (int identifyBuffsI = 0; identifyBuffsI < this.identifyBuffs.size(); identifyBuffsI++) {
			this.writeBean(buf, this.identifyBuffs.get(identifyBuffsI));
		}
		this.writeInt(buf, fengyin, false);
		this.writeInt(buf, zhufu, false);
		this.writeInt(buf, tjId, false);
		writeShort(buf, this.zhufuAttrs.size());
		for (int zhufuAttrsI = 0; zhufuAttrsI < this.zhufuAttrs.size(); zhufuAttrsI++) {
			this.writeBean(buf, this.zhufuAttrs.get(zhufuAttrsI));
		}
		this.writeInt(buf, magicBloodStoneValue, false);
		writeShort(buf, this.xilianAttrs.size());
		for (int xilianAttrsI = 0; xilianAttrsI < this.xilianAttrs.size(); xilianAttrsI++) {
			this.writeBean(buf, this.xilianAttrs.get(xilianAttrsI));
		}
		this.writeInt(buf, demonEquipId, false);
		this.writeInt(buf, gemId, false);
		this.writeInt(buf, aptitudeCfgId, false);
		this.writeInt(buf, aptitudeValue, false);
		writeShort(buf, this.aptitudeAttrs.size());
		for (int aptitudeAttrsI = 0; aptitudeAttrsI < this.aptitudeAttrs.size(); aptitudeAttrsI++) {
			this.writeBean(buf, this.aptitudeAttrs.get(aptitudeAttrsI));
		}
		this.writeInt(buf, toubao, false);
		writeShort(buf, this.suitIdList.size());
		for (int suitIdListI = 0; suitIdListI < this.suitIdList.size(); suitIdListI++) {
			this.writeInt(buf, this.suitIdList.get(suitIdListI), false);
		}
		writeShort(buf, this.diZaoAttribute.size());
		for (int diZaoAttributeI = 0; diZaoAttributeI < this.diZaoAttribute.size(); diZaoAttributeI++) {
			this.writeBean(buf, this.diZaoAttribute.get(diZaoAttributeI));
		}
		this.writeInt(buf, rate, false);
		this.writeInt(buf, xingMingId, false);
		this.writeInt(buf, rare, false);
		writeShort(buf, this.rareAttrs.size());
		for (int rareAttrsI = 0; rareAttrsI < this.rareAttrs.size(); rareAttrsI++) {
			this.writeBean(buf, this.rareAttrs.get(rareAttrsI));
		}
		this.writeInt(buf, magicWeaponRate, false);
		writeShort(buf, this.magicWeaponBlendList.size());
		for (int magicWeaponBlendListI = 0; magicWeaponBlendListI < this.magicWeaponBlendList.size(); magicWeaponBlendListI++) {
			this.writeBean(buf, this.magicWeaponBlendList.get(magicWeaponBlendListI));
		}
		this.writeInt(buf, magicWeaponBuffId, false);
		this.writeInt(buf, shouJueLevel, false);
		this.writeInt(buf, taiTanGemId, false);
		writeShort(buf, this.longhunGemIdList.size());
		for (int longhunGemIdListI = 0; longhunGemIdListI < this.longhunGemIdList.size(); longhunGemIdListI++) {
			this.writeInt(buf, this.longhunGemIdList.get(longhunGemIdListI), false);
		}
		this.writeInt(buf, refinementLevel, false);
		this.writeLong(buf, disarmTime);
		this.writeInt(buf, refinementRate, false);
		this.writeInt(buf, petEquipRate, false);
		this.writeInt(buf, petBuff, false);
		this.writeInt(buf, chaoticGemId, false);
		writeShort(buf, this.xiahunGemIdList.size());
		for (int xiahunGemIdListI = 0; xiahunGemIdListI < this.xiahunGemIdList.size(); xiahunGemIdListI++) {
			this.writeInt(buf, this.xiahunGemIdList.get(xiahunGemIdListI), false);
		}
		this.writeInt(buf, chaoticPowerLevel, false);
		this.writeInt(buf, shenBingZhuHunLevel, false);
		this.writeInt(buf, birthChartLevel, false);
		return true;
	}
}
