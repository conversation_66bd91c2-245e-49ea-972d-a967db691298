package com.sh.game.server;

import java.util.HashMap;
import java.util.Map;

/**
 * ModuleConst
 *
 * <AUTHOR>
 * @date 2020/8/17 11:48
 */
public final class ModuleConst {


    public static final int LOGIC = 1;

    public final static int CLIENT = 2;

    public static final int SCENE = 3;

    public static final Map<String, Integer> MODULE_STR_TO_INT = new HashMap<>();

    static {
        MODULE_STR_TO_INT.put("toServer", LOGIC);
        MODULE_STR_TO_INT.put("toLogic", LOGIC);
        MODULE_STR_TO_INT.put("toClient", CLIENT);
        MODULE_STR_TO_INT.put("toScene", SCENE);
    }
}
