package com.sh.game.remote.rpc.msg;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 模块注册消息
 * <AUTHOR>
 * @date 2020/8/26 15:45
 */
public class RegisterMessage extends AbstractMessage {

    private int moduleId;

    private int port;

    private String host;

    private int channelIndex;

    private String noticeSign;

    private boolean registerFail;



    @Override
    public int getId() {
        return 15;
    }


    @Override
    public boolean read(KryoInput buf) {
        this.moduleId = this.readInt(buf, false);
        this.host = this.readString(buf);
        this.port = this.readInt(buf, false);
        this.channelIndex = this.readInt(buf, false);
        this.noticeSign = this.readString(buf);
        this.registerFail = this.readBoolean(buf);
        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {
        this.writeInt(buf, this.moduleId, false);
        this.writeString(buf, this.host);
        this.writeInt(buf, this.port, false);
        this.writeInt(buf, this.channelIndex, false);
        this.writeString(buf, this.noticeSign);
        this.writeBoolean(buf,this.registerFail);
        return true;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getChannelIndex() {
        return channelIndex;
    }

    public void setChannelIndex(int channelIndex) {
        this.channelIndex = channelIndex;
    }

    public String getNoticeSign() {
        return noticeSign;
    }

    public void setNoticeSign(String noticeSign) {
        this.noticeSign = noticeSign;
    }

    public boolean isRegisterFail() {
        return registerFail;
    }

    public void setRegisterFail(boolean registerFail) {
        this.registerFail = registerFail;
    }
}