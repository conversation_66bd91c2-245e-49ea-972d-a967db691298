package com.sh.game.common.communication.msg.map.fight;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回道具召唤的宠物复活倒计时</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResServantReliveTimeMessage extends ProtobufMessage {

    private com.sh.game.protos.FightProtos.ResServantReliveTime proto;

    private com.sh.game.protos.FightProtos.ResServantReliveTime.Builder builder;

	
	@Override
	public int getId() {
		return 69017;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FightProtos.ResServantReliveTime.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FightProtos.ResServantReliveTime.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FightProtos.ResServantReliveTime.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FightProtos.ResServantReliveTime getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FightProtos.ResServantReliveTime proto) {
        this.proto = proto;
    }

}
