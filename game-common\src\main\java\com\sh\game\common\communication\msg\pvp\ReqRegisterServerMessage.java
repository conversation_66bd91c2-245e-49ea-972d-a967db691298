package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


import java.util.ArrayList;
import java.util.List;

/**
 * <p>注册服务器</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqRegisterServerMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125001;
	}
	
	/**
	 * 注册秘钥
	 */
	private String key;
	/**
	 * 主机
	 */
	private int hostId;
	/**
	 * 服务器id
	 */
	private String ip;
	/**
	 * 端口
	 */
	private int port;
	/**
	 * 服务器类型1.游戏服，2.战斗服
	 */
	private int serverType;
	/**
	 * 支持的pvp类型
	 */
	private List<Integer> pvpTypeList = new ArrayList<>();

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

		public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

		public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

		public int getServerType() {
		return serverType;
	}

	public void setServerType(int serverType) {
		this.serverType = serverType;
	}

		public List<Integer> getPvpTypeList() {
		return pvpTypeList;
	}

	public void setPvpTypeList(List<Integer> pvpTypeList) {
		this.pvpTypeList = pvpTypeList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.key = readString(buf);
		this.hostId = readInt(buf, false);
		this.ip = readString(buf);
		this.port = readInt(buf, false);
		this.serverType = readInt(buf, false);
		int pvpTypeListLength = readShort(buf);
		for (int pvpTypeListI = 0; pvpTypeListI < pvpTypeListLength; pvpTypeListI++) {
			this.pvpTypeList.add(this.readInt(buf, false));
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeString(buf, key);
		this.writeInt(buf, hostId, false);
		this.writeString(buf, ip);
		this.writeInt(buf, port, false);
		this.writeInt(buf, serverType, false);
		writeShort(buf, this.pvpTypeList.size());
		for (int pvpTypeListI = 0; pvpTypeListI < this.pvpTypeList.size(); pvpTypeListI++) {
			this.writeInt(buf, this.pvpTypeList.get(pvpTypeListI), false);
		}
		return true;
	}
}
