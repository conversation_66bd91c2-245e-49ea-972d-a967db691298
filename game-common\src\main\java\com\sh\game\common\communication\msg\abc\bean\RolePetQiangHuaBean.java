package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class RolePetQiangHuaBean extends KryoBean {

	/**
	 * 部位
	 */
	private int pos;
	/**
	 * 等级id
	 */
	private int cfgId;

	public int getPos() {
		return pos;
	}

	public void setPos(int pos) {
		this.pos = pos;
	}

		public int getCfgId() {
		return cfgId;
	}

	public void setCfgId(int cfgId) {
		this.cfgId = cfgId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.pos = readInt(buf, false);
		this.cfgId = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, pos, false);
		this.writeInt(buf, cfgId, false);
		return true;
	}
}
