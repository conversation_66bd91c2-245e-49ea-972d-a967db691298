package com.sh.game.remote.rpc;

import com.sh.game.remote.rpc.msg.ClientConnectCompleteMessage;
import com.sh.game.remote.rpc.msg.MessageTransform;
import com.sh.game.remote.rpc.msg.NoticeTransform;
import com.sh.game.remote.rpc.msg.PingMessage;
import com.sh.game.remote.rpc.msg.RegisterMessage;
import com.sh.game.server.AbstractMessagePool;

import java.util.Map;

/**
 * RPCMessagePool
 *
 * <AUTHOR>
 * @date 2020/8/27 14:08
 */
public class RPCMessagePool extends AbstractMessagePool {

    public RPCMessagePool(Map<String, Integer> moduleConvertMap) {
        super(moduleConvertMap);
        register();
    }

    @Override
    protected void register() {
        register("", new MessageTransform());
        register("", new NoticeTransform());
        register("", new PingMessage());
        register("", new RegisterMessage());
        register("", new ClientConnectCompleteMessage());
    }
}
