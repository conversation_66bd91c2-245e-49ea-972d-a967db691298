package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>返回人皇祭祀信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toClient")
public class ResSacrificeInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 4386;
	}
	
	/**
	 * 全服进度
	 */
	private int systemProgress;
	/**
	 * 是否召唤出祭祀怪物
	 */
	private boolean call;
	/**
	 * 个人进度信息key: 祭祀表配置id, value: 提交次数
	 */
	private List<CommonKeyValueBean> roleProgress = new ArrayList<>();

	public int getSystemProgress() {
		return systemProgress;
	}

	public void setSystemProgress(int systemProgress) {
		this.systemProgress = systemProgress;
	}

		public boolean getCall() {
		return call;
	}

	public void setCall(boolean call) {
		this.call = call;
	}

		public List<CommonKeyValueBean> getRoleProgress() {
		return roleProgress;
	}

	public void setRoleProgress(List<CommonKeyValueBean> roleProgress) {
		this.roleProgress = roleProgress;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.systemProgress = readInt(buf, false);
		this.call = readBoolean(buf);
		int roleProgressLength = readShort(buf);
		for (int roleProgressI = 0; roleProgressI < roleProgressLength; roleProgressI++) {
			if (readByte(buf) == 0) { 
				this.roleProgress.add(null);
			} else {
				CommonKeyValueBean commonKeyValueBean = new CommonKeyValueBean();
				commonKeyValueBean.read(buf);
				this.roleProgress.add(commonKeyValueBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, systemProgress, false);
		this.writeBoolean(buf, call);
		writeShort(buf, this.roleProgress.size());
		for (int roleProgressI = 0; roleProgressI < this.roleProgress.size(); roleProgressI++) {
			this.writeBean(buf, this.roleProgress.get(roleProgressI));
		}
		return true;
	}
}
