package com.sh.game.notice;

import com.sh.common.jdbc.SerializerUtil;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 队列通知消息，主要是用来在不同线程中进行数据的通信
 *
 * <AUTHOR>
 * @date 2018/6/12 16:56
 */
public abstract class ProcessNotice {
    protected static final AtomicInteger INDEX = new AtomicInteger();

    protected transient Set<Integer> hosts = new HashSet<>();

    protected int noticeID;

    protected int noticeIndex;

    protected int processorId;

    protected transient int moduleId;

    protected long key;

    protected transient int childType;

    protected int sourceHost;

    protected byte sourceQueue;

    protected long sourceKey;
    public void decode(byte[] bytes) {
        SerializerUtil.decode(bytes, this);
    }

    public byte[] encode() {
        return SerializerUtil.encode(this);
    }

    public int hostId() {
        return 0;
    }

    public boolean isTimeNotice() {
        return false;
    }

    public void addHost(int hostId) {
        hosts.add(hostId);
    }

    public Collection<Integer> getHosts() {
        return hosts;
    }

    public void addHost(Collection<Integer> hosts) {
        this.hosts.addAll(hosts);
    }


    public int getNoticeIndex() {
        return noticeIndex;
    }

    public void setNoticeIndex(int noticeIndex) {
        this.noticeIndex = noticeIndex;
    }

    public boolean first() {
        return false;
    }

    public int getProcessorId() {
        return processorId;
    }

    public void setProcessorId(int processorId) {
        this.processorId = processorId;
    }

    public int getModuleId() {
        return moduleId;
    }

    public void setModuleId(int moduleId) {
        this.moduleId = moduleId;
    }

    public long getKey() {
        return key;
    }

    public void setKey(long key) {
        this.key = key;
    }

    public int getChildType() {
        return childType;
    }

    public void setChildType(int childType) {
        this.childType = childType;
    }

    public void setSourceHost(int host) {
        this.sourceHost = host;
    }

    public int getSourceHost() {
        return sourceHost;
    }

    public void setSourceQueue(byte sourceQueue) {
        this.sourceQueue = sourceQueue;
    }

    public byte getSourceQueue() {
        return sourceQueue;
    }

    public void setSourceKey(long sourceKey) {
        this.sourceKey = sourceKey;
    }

    public long getSourceKey() {
        return sourceKey;
    }

    public void setNoticeID(int noticeID) {
        this.noticeID = noticeID;
    }

    public int getNoticeID() {
        return noticeID;
    }
}
