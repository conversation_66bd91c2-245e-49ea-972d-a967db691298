<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="SceneBootStrap" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.sh.game.map.startup.boots.MapBootstrap" />
    <module name="game-scene" />
    <option name="PROGRAM_PARAMETERS" value="./conf/scene.properties 1.0.0.1" />
    <option name="VM_PARAMETERS" value="
    -XX:+UseG1GC
    -Xms2g
    -Xmx2g
    -Dgame.log.queueLog=true
    -Dgame.log.MsgUnregister=true
    -Dlog4j.configurationFile=./release/docker/conf/scene/log4j2-scene.xml
    -Dheart.check.delta=600000" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>