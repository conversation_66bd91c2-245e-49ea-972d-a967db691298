package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.pvp.bean.BattleUnionBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>跨服帮战副本创建完成</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCreateSucessMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125020;
	}
	
	/**
	 * 战斗数据
	 */
	private List<BattleUnionBean> battleUnionBean = new ArrayList<>();

	public List<BattleUnionBean> getBattleUnionBean() {
		return battleUnionBean;
	}

	public void setBattleUnionBean(List<BattleUnionBean> battleUnionBean) {
		this.battleUnionBean = battleUnionBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		int battleUnionBeanLength = readShort(buf);
		for (int battleUnionBeanI = 0; battleUnionBeanI < battleUnionBeanLength; battleUnionBeanI++) {
			if (readByte(buf) == 0) { 
				this.battleUnionBean.add(null);
			} else {
				BattleUnionBean battleUnionBean = new BattleUnionBean();
				battleUnionBean.read(buf);
				this.battleUnionBean.add(battleUnionBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.battleUnionBean.size());
		for (int battleUnionBeanI = 0; battleUnionBeanI < this.battleUnionBean.size(); battleUnionBeanI++) {
			this.writeBean(buf, this.battleUnionBean.get(battleUnionBeanI));
		}
		return true;
	}
}
