package com.sh.game.notice;

import com.sh.commons.util.ClassUtil;
import com.sh.commons.util.Md5Util;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;


@Slf4j
public class NoticePool {
    private final Map<Integer, Class<? extends ProcessNotice>> NOTICE_MAP = new HashMap<>();
    private final Map<Class<?>, Integer> NOTICE_ID = new HashMap<>();
    private final Map<Integer, NoticeHandler> HANDLER_MAP = new HashMap<>();
    private String sign;

    public NoticePool(String actionPkg) {
        initNotice();
        initAction(actionPkg);
    }

    public ProcessNotice getNotice(int id) {
        Class<? extends ProcessNotice> clazz = NOTICE_MAP.get(id);
        if (clazz == null) {
            throw new RuntimeException("unknown notice: " + id);
        }
        try {
            ProcessNotice notice = clazz.newInstance();
            return notice;
        } catch (Exception e) {
            log.error("create notice failed: {}", clazz.getName());
        }

        return null;
    }

    public NoticeHandler getHandler(int id) {
        NoticeHandler handler = HANDLER_MAP.get(id);
        if (handler == null) {
            throw new RuntimeException("notice handler not found: " + id + "/" + NOTICE_MAP.get(id));
        }

        return handler;
    }

    public int getNoticeId(Class<?> clazz) {
        Integer ID = NOTICE_ID.get(clazz);
        if (ID == null) {
            throw new RuntimeException("unknown notice: " + clazz.getName());
        }
        return ID;
    }

    private void initNotice() {
        try {
            Set<Class<?>> classes = ClassUtil.findClassWithAnnotation(NoticePool.class.getClassLoader(), "com.sh.game.common.communication.notice", Notice.class);
            Set<Class<?>> orderedClasses = new TreeSet<>(Comparator.comparing(Class::getName));
            orderedClasses.addAll(classes);
            StringBuilder builder = new StringBuilder();

            int ID = 0;
            for (Class<?> clazz: orderedClasses) {
                if (!ProcessNotice.class.isAssignableFrom(clazz)) {
                    continue;
                }

                ID ++;
                NOTICE_ID.put(clazz, ID);
                NOTICE_MAP.put(ID, (Class<? extends ProcessNotice>) clazz);
                builder
                        .append(ID)
                        .append("/")
                        .append(clazz.getName())
                        .append("\n");
            }
            this.sign = Md5Util.md5(builder.toString());
            log.debug("notice pool >>\n{}{}", builder.toString(), this.sign);
        } catch (Exception e) {
            throw new RuntimeException("notice init failed. ", e);
        }
    }

    private void initAction(String pkg) {
        try {
            Set<Class<?>> classes = ClassUtil.findClassWithAnnotation(NoticePool.class.getClassLoader(), pkg, NoticeAction.class);
            for (Class<?> clazz: classes) {
                Object noticeAction = null;
                Method[] methods = clazz.getMethods();
                for (Method method : methods) {
                    Parameter[] parameters = method.getParameters();
                    if (parameters.length != 1) {
                        continue;
                    }
                    Parameter parameter = parameters[0];
                    if (ProcessNotice.class.isAssignableFrom(parameter.getType())) {
                        if (noticeAction == null) {
                            noticeAction = clazz.newInstance();
                        }
                        NoticeHandler handler = new NoticeHandler(noticeAction, method);
                        HANDLER_MAP.put(getNoticeId(parameter.getType()), handler);
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("notice action init failed. ", e);
        }
    }

    public String getSign() {
        return sign;
    }
}
