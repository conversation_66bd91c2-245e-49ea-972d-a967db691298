package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回侠侣冲级活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResActXiaLvRushLevelMessage extends ProtobufMessage {

    private com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel proto;

    private com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel.Builder builder;

	
	@Override
	public int getId() {
		return 4274;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ActivityProtos.ResActXiaLvRushLevel proto) {
        this.proto = proto;
    }

}
