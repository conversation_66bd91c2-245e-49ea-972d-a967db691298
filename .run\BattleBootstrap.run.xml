<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="BattleBootstrap" type="Application" factoryName="Application">
    <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <option name="MAIN_CLASS_NAME" value="com.sh.game.pvp.battle.PVPServerBootstrap" />
    <module name="game-package-battle" />
    <option name="PROGRAM_PARAMETERS" value="./conf/battle.properties 1.0.0.1" />
    <option name="VM_PARAMETERS" value="
    -XX:+UseG1GC
    -Xms512m
    -Xmx1g
    -Dgame.log.queueLog=true
    -Dgame.log.MsgUnregister=true
    -Dlog4j.configurationFile=./release/docker/conf/battle/log4j2-battle.xml
    -Dheart.check.delta=600000" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>