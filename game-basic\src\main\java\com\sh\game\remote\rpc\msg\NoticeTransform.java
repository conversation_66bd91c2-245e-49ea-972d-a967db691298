package com.sh.game.remote.rpc.msg;

import com.sh.game.notice.ProcessNotice;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * Notice消息传输
 * <AUTHOR>
 * @date 2018/8/27 18:11
 */
public class NoticeTransform extends AbstractMessage {

    /**
     * 玩家id列表
     */
    private byte processId;

    private int noticeId;

    private long playerId;

    private byte[] noticeBytes;

    private ProcessNotice notice;

    private boolean encode = true;


    public void encodeTransform() {
        this.noticeBytes = notice.encode();
    }

    @Override
    public int getId() {
        return 13;
    }

    @Override
    public boolean write(KryoOutput output) {
        writeByte(output, processId);
        writeInt(output,noticeId, false);
        writeLong(output, playerId);
        writeBytes(output,noticeBytes);
        return true;
    }

    @Override
    public boolean read(KryoInput input) {
        this.processId = readByte(input);
        this.noticeId = readInt(input, false);
        this.playerId = readLong(input);
        this.noticeBytes = readBytes(input);

        return true;
    }

    public byte getProcessId() {
        return processId;
    }

    public void setProcessId(byte processId) {
        this.processId = processId;
    }

    public int getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(int noticeId) {
        this.noticeId = noticeId;
    }

    public byte[] getNoticeBytes() {
        return noticeBytes;
    }

    public void setNoticeBytes(byte[] noticeBytes) {
        this.noticeBytes = noticeBytes;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public ProcessNotice getNotice() {
        return notice;
    }

    public void setNotice(ProcessNotice notice) {
        this.notice = notice;
    }

    public boolean isEncode() {
        return encode;
    }

    public void setEncode(boolean encode) {
        this.encode = encode;
    }
}
