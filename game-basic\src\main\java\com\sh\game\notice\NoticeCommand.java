package com.sh.game.notice;

import com.sh.concurrent.AbstractCommand;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

/**
 * 执行notice的command载体
 *
 * <AUTHOR>
 * @date 2020/8/18 13:24
 */
@Slf4j
public class NoticeCommand extends AbstractCommand {

    private Object action;

    private Method method;

    private ProcessNotice notice;

    public NoticeCommand(Object action, Method method, ProcessNotice notice) {
        this.action = action;
        this.method = method;
        this.notice = notice;
    }

    @Override
    public void doAction() {
        try {
            method.invoke(action, notice);
        } catch (Exception e) {
            String errorInfo = String.format("notice执行出错,notice:[%s],handler:[%s],method:[%s]",
                    notice.getClass().getName(), action.getClass().getName(), method.getName());
            log.error(errorInfo, e);
        }
    }

    public Method getMethod() {
        return method;
    }
}