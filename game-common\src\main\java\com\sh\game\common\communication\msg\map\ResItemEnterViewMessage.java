package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>道具进入视野</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResItemEnterViewMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResItemEnterView proto;

    private com.sh.game.protos.MapProtos.ResItemEnterView.Builder builder;

	
	@Override
	public int getId() {
		return 67015;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResItemEnterView.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResItemEnterView.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResItemEnterView.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResItemEnterView getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResItemEnterView proto) {
        this.proto = proto;
    }

}
