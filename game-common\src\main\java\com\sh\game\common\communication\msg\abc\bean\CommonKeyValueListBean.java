package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonKeyValueListBean extends KryoBean {

	/**
	 * 
	 */
	private int key;
	/**
	 * 
	 */
	private List<Integer> valueList = new ArrayList<>();

	public int getKey() {
		return key;
	}

	public void setKey(int key) {
		this.key = key;
	}

		public List<Integer> getValueList() {
		return valueList;
	}

	public void setValueList(List<Integer> valueList) {
		this.valueList = valueList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.key = readInt(buf, false);
		int valueListLength = readShort(buf);
		for (int valueListI = 0; valueListI < valueListLength; valueListI++) {
			this.valueList.add(this.readInt(buf, false));
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, key, false);
		writeShort(buf, this.valueList.size());
		for (int valueListI = 0; valueListI < this.valueList.size(); valueListI++) {
			this.writeInt(buf, this.valueList.get(valueListI), false);
		}
		return true;
	}
}
