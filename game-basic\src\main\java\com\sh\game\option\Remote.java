package com.sh.game.option;

import lombok.Getter;

@Getter
public class Remote {

    private final String host;
    private final int port;

    public Remote(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public static boolean isSame(Remote remote1, Remote remote2) {
        if (remote1 == null) {
            return remote2 == null;
        }
        if (remote2 == null) {
            return false;
        }

        return remote1.host.equals(remote2.host) && remote1.port == remote2.port;
    }

    @Override
    public String toString() {
        return "[" + host + ":" + port + "]";
    }
}
