package com.sh.game.common.communication.msg.map.duplicate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求进入天下第一副本</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqEnterWorldFirstMessage extends ProtobufMessage {

    private com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst proto;

    private com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst.Builder builder;

	
	@Override
	public int getId() {
		return 71018;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DuplicateProtos.ReqEnterWorldFirst proto) {
        this.proto = proto;
    }

}
