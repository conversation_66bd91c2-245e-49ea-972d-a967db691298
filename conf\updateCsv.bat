@echo off
setlocal

rem svn 地址
set "dataRepo=https://192.168.10.196/svn/gd_h5/data/miracle-data"

rem 设置要操作的目录路径
set "dataDir=./data"

rem 检查data目录是否存在，不存在则创建
if not exist "%dataDir%" (
    mkdir "%dataDir%"
    echo Data directory created.
)

rem 创建日志文件夹
set "logDir=../logs"

rem 检查logs目录是否存在，不存在则创建
if not exist "%logDir%" (
    mkdir "%logDir%"
)

rem 切换到data目录
cd /d "%dataDir%"

rem 检查当前目录是否为SVN工作副本
svn info > nul 2>&1
if %errorlevel% neq 0 (
    echo Not a SVN working copy. Checking out repository...
    svn checkout "%dataRepo%" .
) else (
    echo Repository exists. Proceeding with updates...
    svn revert -R .
    svn cleanup
    svn up .
)

endlocal