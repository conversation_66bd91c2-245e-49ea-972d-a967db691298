<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="StopServer" type="Application" factoryName="Application">
    <option name="MAIN_CLASS_NAME" value="com.sh.game.back.stat.StopServerClient" />
    <module name="game-package-game" />
    <option name="PROGRAM_PARAMETERS" value="./conf/game.properties 1.0.0.1" />
    <option name="VM_PARAMETERS" value="
    -Xms64m
    -Xmx64m
    -Xloggc:./logs/sgc.log
    -XX:HeapDumpPath=./heapdump.hprof
    -Dfile.encoding=UTF-8
    -Dlog4j.configurationFile=./release/docker/conf/game/log4j2-game.xml" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>