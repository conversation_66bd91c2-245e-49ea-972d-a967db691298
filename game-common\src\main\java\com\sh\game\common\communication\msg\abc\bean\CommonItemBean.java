package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonItemBean extends KryoBean {

	/**
	 * 唯一id
	 */
	private long uid;
	/**
	 * 道具配置id
	 */
	private int itemId;
	/**
	 * 数量
	 */
	private int count;
	/**
	 * 过期时间
	 */
	private int expire;
	/**
	 * 已经使用的次数
	 */
	private int uTimes;
	/**
	 * 道具获得时间
	 */
	private int time;
	/**
	 * 道具参数
	 */
	private List<Integer> params = new ArrayList<>();
	/**
	 * 装备属性（null表示没有）
	 */
	private EquipDataBean equipData;
	/**
	 * 固化数据
	 */
	private ItemImmobilization immobilization;
	/**
	 * 来源
	 */
	private ItemFromBean from;

	public long getUid() {
		return uid;
	}

	public void setUid(long uid) {
		this.uid = uid;
	}

		public int getItemId() {
		return itemId;
	}

	public void setItemId(int itemId) {
		this.itemId = itemId;
	}

		public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

		public int getExpire() {
		return expire;
	}

	public void setExpire(int expire) {
		this.expire = expire;
	}

		public int getUTimes() {
		return uTimes;
	}

	public void setUTimes(int uTimes) {
		this.uTimes = uTimes;
	}

		public int getTime() {
		return time;
	}

	public void setTime(int time) {
		this.time = time;
	}

		public List<Integer> getParams() {
		return params;
	}

	public void setParams(List<Integer> params) {
		this.params = params;
	}
	public EquipDataBean getEquipData() {
		return equipData;
	}

	public void setEquipData(EquipDataBean equipData) {
		this.equipData = equipData;
	}

		public ItemImmobilization getImmobilization() {
		return immobilization;
	}

	public void setImmobilization(ItemImmobilization immobilization) {
		this.immobilization = immobilization;
	}

		public ItemFromBean getFrom() {
		return from;
	}

	public void setFrom(ItemFromBean from) {
		this.from = from;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.uid = readLong(buf);
		this.itemId = readInt(buf, false);
		this.count = readInt(buf, false);
		this.expire = readInt(buf, false);
		this.uTimes = readInt(buf, false);
		this.time = readInt(buf, false);
		int paramsLength = readShort(buf);
		for (int paramsI = 0; paramsI < paramsLength; paramsI++) {
			this.params.add(this.readInt(buf, false));
		}
		if (readByte(buf) != 0) {
			EquipDataBean equipDataBean = new EquipDataBean();
			equipDataBean.read(buf);
			this.equipData = equipDataBean;
		}
		if (readByte(buf) != 0) {
			ItemImmobilization itemImmobilization = new ItemImmobilization();
			itemImmobilization.read(buf);
			this.immobilization = itemImmobilization;
		}
		if (readByte(buf) != 0) {
			ItemFromBean itemFromBean = new ItemFromBean();
			itemFromBean.read(buf);
			this.from = itemFromBean;
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, uid);
		this.writeInt(buf, itemId, false);
		this.writeInt(buf, count, false);
		this.writeInt(buf, expire, false);
		this.writeInt(buf, uTimes, false);
		this.writeInt(buf, time, false);
		writeShort(buf, this.params.size());
		for (int paramsI = 0; paramsI < this.params.size(); paramsI++) {
			this.writeInt(buf, this.params.get(paramsI), false);
		}
		this.writeBean(buf, equipData);
		this.writeBean(buf, immobilization);
		this.writeBean(buf, from);
		return true;
	}
}
