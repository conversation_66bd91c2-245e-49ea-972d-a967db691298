package com.sh.game.server;

import com.sh.net.Message;
import com.sh.net.MessagePool;
import com.sh.server.AbstractMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * AbstractMessagePool
 *
 * <AUTHOR>
 * @date 2020/8/26 11:41
 */
@Slf4j
public abstract class AbstractMessagePool implements MessagePool {
    protected Map<Integer,  Class<? extends AbstractMessage>> msgPool = new HashMap<>();

    protected Map<Integer,  Integer> moduleIdPool = new HashMap<>();

    protected Map<String, Integer> moduleConvertMap;

    protected AbstractMessagePool(Map<String, Integer> moduleConvertMap) {
        this.moduleConvertMap = moduleConvertMap;
    }

    public int getModuleId(int msgId) {
        return this.moduleIdPool.getOrDefault(msgId,0);
    }

    @Override
    public Message getMessage(int messageId) {
        Class<? extends AbstractMessage> clazz = msgPool.get(messageId);
        if (clazz != null) {
            try {
                AbstractMessage msg = clazz.getDeclaredConstructor().newInstance();
                return msg;
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }

    public <T extends AbstractMessage> void register(String module, T message) {
        Integer moduleId = moduleConvertMap.get(module);
        if (this.msgPool.containsKey(message.getId())) {
            Class<? extends AbstractMessage> existMsg = this.msgPool.get(message.getId());
            log.warn("消息号[{"+message.getId()+"}]注册重复,已注册：{"+existMsg.getName()+"},准备注册：{"+message.getClass().getName()+"}");
            return;
        }
        this.msgPool.put(message.getId(), message.getClass());
        this.moduleIdPool.put(message.getId(), moduleId);
    }

    protected void register() {
//        register("", new com.sh.game.remote.transform.NoticeTransform());
//        register("", new com.sh.game.remote.transform.ReqMsgToMapTransform());
//        register("", new com.sh.game.remote.transform.ResMsgToAllHostPlayerTransform());
//        register("", new com.sh.game.remote.transform.ResMsgToPlayerTransform());
//        register("", new com.sh.game.remote.transform.ResMsgToWorldTransform());

    }
}
