package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求切换攻击模式</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqSwitchFightModelMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ReqSwitchFightModel proto;

    private com.sh.game.protos.MapProtos.ReqSwitchFightModel.Builder builder;

	
	@Override
	public int getId() {
		return 67029;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ReqSwitchFightModel.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ReqSwitchFightModel.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ReqSwitchFightModel.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ReqSwitchFightModel getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ReqSwitchFightModel proto) {
        this.proto = proto;
    }

}
