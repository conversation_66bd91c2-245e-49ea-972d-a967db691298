package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>通知匹配服创建比赛副本完毕</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCreateBattleMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125010;
	}
	
	/**
	 * pvpid
	 */
	private int battleId;
	/**
	 * 地图id
	 */
	private int mapId;

	public int getBattleId() {
		return battleId;
	}

	public void setBattleId(int battleId) {
		this.battleId = battleId;
	}

		public int getMapId() {
		return mapId;
	}

	public void setMapId(int mapId) {
		this.mapId = mapId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.battleId = readInt(buf, false);
		this.mapId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, battleId, false);
		this.writeInt(buf, mapId, false);
		return true;
	}
}
