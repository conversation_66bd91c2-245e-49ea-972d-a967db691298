1. 条件格式支持与、或、非，可以通过小括号改变判断的优先级

2. 配置格式

    条件类型#参数[#更多参数]
    需要明确的是，不允许只配置条件类型，即任何条件都必须有至少一个条件参数

3. 一些例子

    假设：
    
        1 等级
        2 转生等级
        3 vip等级

    1#1#99&2#10 表示等级在1到99之间并且转生等级大于10级
    
    (1#1#99&2#10)|3#3 表示 等级在1到99之间并且转生等级大于10级 或者 vip等级大于3级
    
    1#10|(2#10) 表示转生等级大于10级或者等级大于10级（判断优先级）
    
    -1#10 表示等级必须小于10
    
    -（1#10&2#10）表示等级小于10或者转生等级小于10
    
4. 另外，关于条件参数的配置形式，在保证至少一个条件参数的前提下，可以与具体做条件的同事约定。
比如1#1#99表示等级在1到99区间，而1#1表示等级大于1（上限不填）

    一般约定条件参数的配置使用闭区间    
    