package com.sh.game.notice;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 有实效的notice
 * <AUTHOR>
 * @date 2019/1/13 14:31
 */
@Getter
@Setter
@Slf4j
public abstract class TimeProcessNotice extends ProcessNotice {

    /**
     * 开始时间
     */
    private transient long startTime = System.currentTimeMillis();

    /**
     * 超时时间
     */
    private transient long waitTime = 2000;

    /**
     * 超时处理器
     * 一般用于地图请求超时(失败)，给与默认地图防止客户端不能进入等问题。
     * 基本用来给与默认值或者参数
     */
    private transient TimeoutHandler handler;

    /**
     * 回调
     * notice成功执行后，执行
     */
    private transient NoticeCallback callback;


    public TimeProcessNotice() {
        this.noticeIndex = INDEX.incrementAndGet();
    }

    public boolean isTimeNotice() {
        return true;
    }

    public boolean checkTimeout(long curTime) {
        if (curTime - startTime > waitTime) {
            log.info("notice请求超时：{}", this.getClass().getName());
            if (handler != null) {
                handler.timeout(this);
            }
            return true;
        }
        return false;
    }

    /**
     *  是否是流程的发起者，即第一个notice
     * @return
     */
    public boolean first() {
        return false;
    }

}
