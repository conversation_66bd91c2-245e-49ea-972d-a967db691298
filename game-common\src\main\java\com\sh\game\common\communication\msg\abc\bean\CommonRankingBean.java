package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonRankingBean extends KryoBean {

	/**
	 * 
	 */
	private int ranking;
	/**
	 * 
	 */
	private long uid;
	/**
	 * 
	 */
	private String name;
	/**
	 * 
	 */
	private long score;

	public int getRanking() {
		return ranking;
	}

	public void setRanking(int ranking) {
		this.ranking = ranking;
	}

		public long getUid() {
		return uid;
	}

	public void setUid(long uid) {
		this.uid = uid;
	}

		public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

		public long getScore() {
		return score;
	}

	public void setScore(long score) {
		this.score = score;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.ranking = readInt(buf, false);
		this.uid = readLong(buf);
		this.name = readString(buf);
		this.score = readLong(buf);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, ranking, false);
		this.writeLong(buf, uid);
		this.writeString(buf, name);
		this.writeLong(buf, score);
		return true;
	}
}
