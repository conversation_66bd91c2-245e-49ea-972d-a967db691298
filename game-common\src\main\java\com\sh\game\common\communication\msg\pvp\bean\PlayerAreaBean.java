package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class PlayerAreaBean extends KryoBean {

	/**
	 * 玩家唯一id
	 */
	private long rid;
	/**
	 * 赛区
	 */
	private int area;

	public long getRid() {
		return rid;
	}

	public void setRid(long rid) {
		this.rid = rid;
	}

		public int getArea() {
		return area;
	}

	public void setArea(int area) {
		this.area = area;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.rid = readLong(buf);
		this.area = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, rid);
		this.writeInt(buf, area, false);
		return true;
	}
}
