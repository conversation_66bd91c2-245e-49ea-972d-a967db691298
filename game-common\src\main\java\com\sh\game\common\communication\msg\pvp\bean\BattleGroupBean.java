package com.sh.game.common.communication.msg.pvp.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class BattleGroupBean extends KryoBean {

	/**
	 * 组id
	 */
	private long groupId;
	/**
	 * 服务器id
	 */
	private int hostId;
	/**
	 * 玩家列表
	 */
	private List<BattlePlayerBean> playerList = new ArrayList<>();
	/**
	 * 分组1.A组，2.B组
	 */
	private int team;

	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

		public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public List<BattlePlayerBean> getPlayerList() {
		return playerList;
	}

	public void setPlayerList(List<BattlePlayerBean> playerList) {
		this.playerList = playerList;
	}
	public int getTeam() {
		return team;
	}

	public void setTeam(int team) {
		this.team = team;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.groupId = readLong(buf);
		this.hostId = readInt(buf, false);
		int playerListLength = readShort(buf);
		for (int playerListI = 0; playerListI < playerListLength; playerListI++) {
			if (readByte(buf) == 0) { 
				this.playerList.add(null);
			} else {
				BattlePlayerBean battlePlayerBean = new BattlePlayerBean();
				battlePlayerBean.read(buf);
				this.playerList.add(battlePlayerBean);
			}
		}
		this.team = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeLong(buf, groupId);
		this.writeInt(buf, hostId, false);
		writeShort(buf, this.playerList.size());
		for (int playerListI = 0; playerListI < this.playerList.size(); playerListI++) {
			this.writeBean(buf, this.playerList.get(playerListI));
		}
		this.writeInt(buf, team, false);
		return true;
	}
}
