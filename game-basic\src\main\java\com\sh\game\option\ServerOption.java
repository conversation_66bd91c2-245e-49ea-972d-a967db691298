package com.sh.game.option;

import com.sh.commons.util.StringUtil;
import com.sh.commons.util.Symbol;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.Properties;

/**
 * 游戏选项
 *
 * <AUTHOR>
 * Date 2015-3-25 下午9:12:54
 */
@lombok.Getter
@lombok.Setter
@Slf4j
public class ServerOption {

    public interface KEY {
        String PLATFORM_ID = "platformId";
        String SERVER_ID = "serverId";
        String SERVER_TYPE = "serverType";
        String GAME_SERVER_PORT = "gameServerPort";
        String BACK_SERVER_PORT = "backServerPort";
        String HTTP_SERVER_PORT = "httpServerPort";
        String OPEN_TIME = "openTime";
        String MERGE_TIME = "combineTime";

        String CONFIG_DATA_PATH = "configDataPath";
        String GAME_DB_CONFIG_PATH = "gameDbConfigPath";
        String LOG_DB_CONFIG_PATH = "logDBConfigPath";
        String HTTP_DB_CONFIG_PATH = "httpDBConfigPath";
        String REDIS_CONFIG_PATH = "redisConfigPath";
        String API_MD5 = "apiMd5";
        String BACK_LOGIN_SIGN = "backLoginSign";

        String DEBUG = "debug";
        String FCM_CHECK = "fcmCheck";
        String SERVER_ID_VERIFY = "serverIdVerify";//服务器编号验证
        String ADD_LOGIN_WHITE_LIST = "addLoginWhiteIp";
        String ADD_LOGIN_WHITE_ACCOUNT_LIST = "addLoginWhiteAccount";
        String ADD_RECHARGE_WHITE_LIST = "addRechargeWhiteIp";
        String CHAT_CHECK_MODE = "chatMode";

        String HOST = "host";

        /**
         * 操作系统类型
         */
        String OS = "os";


        /**
         * 邀请码相关
         */
        //邀请码生成
        String INVITATION_CREATE_URL = "InvitationCreatUrl";
        //邀请码使用
        String INVITATION_USED_URL = "InvitationUseUrl";
        //邀请码开启日期
        String INVITATION_STATUS_URL = "InvitationStateUrl";

        String INVITATION_DAY_STATUS = "invitationDay";

        //全平台充值排行榜
        String PLATFORM_RECHARGED_RANK_URL = "platformRechargeRankUrl";

        /**
         * 协议访问记录参数
         */
        String PROTOCOL_LIMIT_LOG = "protocol.limit.log";
        String PROTOCOL_LIMIT_LOG_DELAY = "protocol.limit.log.delay";
        String PROTOCOL_LIMIT_LOG_RATE = "protocol.limit.log.rate";

        /**
         * 匹配相关
         */
        String MATCH_GROUP = "match.group";
        String MATCH_ADDRESS = "match.address";
        String MATCH_KEY = "match.key";
        String MATCH_DFLS_GROUP = "match.dfls.group";

        /**
         * ssl相关
         */
        String SSL_KEY_CERTCHAIN_FILE = "sslKeyCertChainFile";
        String SSL_KEY_FILE = "sslKeyFile";
        String SSL = "ssl";

        /**
         * 跨服
         */
        String REMOTE_ADDRESS = "remoteAddress";

        String REMOTE_INFO_TYPE = "remoteInfoType";

        String REMOTE_INFO_API = "remoteInfoAPI";
    }

    /**
     * config.properties 配置地址
     */
    protected String configPath;
    /**
     * 版本
     */
    protected int version;


    /**
     * 平台id（自定）
     */
    protected int platformId;
    /**
     * 服务器ID
     */
    protected int serverId;
    /**
     * 服务器类型1：游戏服
     */
    protected int serverType = 1;
    /**
     * 游戏服务器端口
     */
    protected int gameServerPort;
    /**
     * 后台服务器端口
     */
    protected int backServerPort;
    /**
     * http服务器端口
     */
    private int httpServerPort;
    /**
     * 跨服
     */
    private Remote crossRemote;

    /**
     * 跨服信息获取的方式 1 本地配置，2 api接口
     */
    private int remoteInfoType;

    /**
     * 获取远程服务器信息的接口
     */
    private String remoteInfoAPI="https://api.sbxxwxxyx.soletower.com/common/open/selectKuafu";

    /**
     * 开服日期
     */
    private LocalDateTime openTime;
    /**
     * 开服时间戳(秒)
     */
    private long openTimestamp;
    /**
     * 开服0点时间戳
     */
    private long openZeroTimestamp;
    /**
     * 合服日期
     */
    private LocalDateTime combineTime;
    /**
     * 合服时间戳(秒)
     */
    private long mergeTimestamp;
    /**
     * 合服0点时间戳
     */
    private long mergeZeroTimeStamp;


    /**
     * data文件所在路径
     */
    protected String configDataPath;
    /**
     * 游戏服数据库连接池配置文件
     */
    protected String gameDbConfigPath;
    /**
     * 日志数据库连接池配置文件
     */
    private String logDBConfigPath;
    /**
     * http后台服务器数据库连接池配置文件
     */
    private String httpDBConfigPath;
    /**
     * redis配置文件
     */
    protected String redisConfigPath;
    /**
     * MD5 key
     */
    private String apiMd5;
    protected String backLoginSign;


    /**
     * 调试模式
     */
    private boolean debug;
    /**
     * 是否开启防沉迷
     */
    private boolean fcmCheck;
    /**
     * ssl cert文件
     */
    private String sslKeyCertChainFile;
    /**
     * ssl key文件
     */
    private String sslKeyFile;
    private boolean ssl;

    private int chatMode = 0;

    private int os = 0;

    /**
     * 登录服地址
     */
    private String loginUrl;
    private String loginKey;

    //邀请码生成
    String invitationCreateUrl;
    //邀请码使用
    String invitationUsedUrl;
    //邀请码开启日期
    String invitationOpenUrl;

    // 是否开启信息访问记录
    private boolean protocolLimitLog;

    // 会话建立后开始记录的时间
    private int protocolLimitLogDelay = 30_000;

    // 单个会话每秒允许的访问次数
    private int protocolLimitLogRate = 50;


    private String host;

    //全平台充值排行榜
    String platformRechargeRankUrl;
    /**
     * 匹配相关
     *
     * @param configFile
     */
    int matchGroup;
    Remote matchRemote;
    String matchKey;
    int dflsGroup;


    public Properties build(String configFile) {
        this.configPath = configFile;

        OrderedProperties properties = read();
        this.platformId = Integer.parseInt(properties.getProperty(KEY.PLATFORM_ID));
        // validate
        if (this.platformId > 511) {
            throw new IllegalArgumentException("平台id最大支持511，请合理配置. " + this.platformId);
        }
        this.serverId = Integer.parseInt(properties.getProperty(KEY.SERVER_ID));
        this.serverType = Integer.parseInt(properties.getProperty(KEY.SERVER_TYPE));
        if ((properties.getProperty(KEY.SERVER_ID_VERIFY) == null || Boolean.parseBoolean(properties.getProperty(KEY.SERVER_ID_VERIFY)))
                && (this.serverId > 16383 || this.serverId <= 0) ) {
            throw new IllegalArgumentException("服务器id区间为[1,16383]，请合理配置. " + this.serverId);
        }
        this.gameServerPort = Integer.parseInt(properties.getProperty(KEY.GAME_SERVER_PORT));
        this.backServerPort = Integer.parseInt(properties.getProperty(KEY.BACK_SERVER_PORT));
        this.httpServerPort = Integer.parseInt(Optional.ofNullable(properties.getProperty(KEY.HTTP_SERVER_PORT)).orElse("0"));

        //跨服
        this.remoteInfoType = Integer.parseInt(properties.getProperty(KEY.REMOTE_INFO_TYPE, "1"));
        if (this.remoteInfoType == 1) { //本地配置
            this.update(KEY.REMOTE_ADDRESS, properties.getProperty(KEY.REMOTE_ADDRESS), false);
        } else {
            this.remoteInfoAPI = properties.getProperty(KEY.REMOTE_INFO_API, "1");
        }


        if (this.getServerType() == 1) {
            this.update(KEY.OPEN_TIME, properties.getProperty(KEY.OPEN_TIME), false);
            this.update(KEY.MERGE_TIME, properties.getProperty(KEY.MERGE_TIME), false);
        } else {
            // 跨服初始化开服时间
            this.update(KEY.OPEN_TIME, "2030-01-01 00:00:00", false);
        }

        this.configDataPath = properties.getProperty(KEY.CONFIG_DATA_PATH);
        this.redisConfigPath = properties.getProperty(KEY.REDIS_CONFIG_PATH);
        this.gameDbConfigPath = properties.getProperty(KEY.GAME_DB_CONFIG_PATH);
        this.logDBConfigPath = properties.getProperty(KEY.LOG_DB_CONFIG_PATH);
        this.httpDBConfigPath = properties.getProperty(KEY.HTTP_DB_CONFIG_PATH);
        this.apiMd5 = properties.getProperty(KEY.API_MD5);
        this.backLoginSign = properties.getProperty(KEY.BACK_LOGIN_SIGN);

        this.debug = Boolean.parseBoolean(properties.getProperty(KEY.DEBUG));
        this.fcmCheck = Boolean.parseBoolean(properties.getProperty(KEY.FCM_CHECK));

        String chatMode = properties.getProperty(KEY.CHAT_CHECK_MODE);
        if (chatMode != null) {
            this.chatMode = Integer.parseInt(properties.getProperty(KEY.CHAT_CHECK_MODE));
            log.info("系统配置参数，聊天检查类型为：{}", this.chatMode);
        }
        this.loginUrl = properties.getProperty("loginUrl");
        this.loginKey = properties.getProperty("loginKey");


        String os = properties.getProperty(KEY.OS);
        if (os != null) {
            this.os = Integer.parseInt(os);
            log.info("系统配置参数，游戏操作系统类型os为：{}", this.os);
        }


        //邀请码生成
        this.invitationCreateUrl=properties.getProperty(KEY.INVITATION_CREATE_URL);
        //邀请码使用
        this.invitationUsedUrl = properties.getProperty(KEY.INVITATION_USED_URL);
        //邀请码开启日期
        this.invitationOpenUrl=properties.getProperty(KEY.INVITATION_STATUS_URL);

        //全平台充值排行榜
        this.platformRechargeRankUrl=properties.getProperty(KEY.PLATFORM_RECHARGED_RANK_URL);

        String protocolLimitLog = properties.getProperty(KEY.PROTOCOL_LIMIT_LOG);
        if (protocolLimitLog != null) {
            this.protocolLimitLog = Boolean.parseBoolean(protocolLimitLog);
            log.info("系统配置参数，会话建立后是否访问超限是否记录：{}", this.protocolLimitLog);
        }

        String protocolLimitLogDelay = properties.getProperty(KEY.PROTOCOL_LIMIT_LOG_DELAY);
        if (protocolLimitLogDelay != null) {
            this.protocolLimitLogDelay = Integer.parseInt(protocolLimitLogDelay);
            log.info("系统配置参数，会话建立后开启访问记录延迟：{}", this.protocolLimitLogDelay);
        }

        String protocolLimitLogRate = properties.getProperty(KEY.PROTOCOL_LIMIT_LOG_RATE);
        if (protocolLimitLogRate != null) {
            this.protocolLimitLogRate = Integer.parseInt(protocolLimitLogRate);
            log.info("系统配置参数，单个会话每秒允许访问的次数：{}", this.protocolLimitLogRate);
        }

        String propHost = properties.getProperty(KEY.HOST);
        if (propHost != null) {
            this.host = propHost;
        }


        /**
         * 匹配相关
         */
        String propMatchGroup = properties.getProperty(KEY.MATCH_GROUP);
        if (propMatchGroup != null) {
            this.matchGroup = Integer.parseInt(propMatchGroup);
        }
        this.update(KEY.MATCH_ADDRESS, properties.getProperty(KEY.MATCH_ADDRESS), false);
        String propMatchKey=  properties.getProperty(KEY.MATCH_KEY);
        if (propMatchKey != null) {
            this.matchKey = propMatchKey;
            log.info("系统配置参数，匹配服密钥 ：{}", this.matchKey);
        }
        String propDflsGroup = properties.getProperty(KEY.MATCH_DFLS_GROUP);
        if (propDflsGroup != null) {
            this.dflsGroup = Integer.parseInt(propDflsGroup);
        }

        //SSL证书相关
        this.sslKeyCertChainFile = properties.getProperty(KEY.SSL_KEY_CERTCHAIN_FILE);
        this.sslKeyFile = properties.getProperty(KEY.SSL_KEY_FILE);
        this.ssl = Boolean.parseBoolean(properties.getProperty(KEY.SSL, "false"));

        return properties;

    }

    public Object update(String key, String value, boolean save) {
        Object origin;

        switch (key) {
            case KEY.REMOTE_ADDRESS:
                origin = this.crossRemote;

                try {
                    String[] temp = value.split(Symbol.MAOHAO);
                    this.crossRemote = new Remote(temp[0], Integer.parseInt(temp[1]));
                } catch (Exception e) {
                    this.crossRemote = null;
                }
                log.info("跨服变化#变更：{} -> {}，是否启动时的变化：{}", origin, this.crossRemote, !save);
                break;
            case KEY.OPEN_TIME:
                origin = this.openTime;

                this.openTime = LocalDateTime.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                this.openTimestamp = this.openTime.atZone(ZoneId.systemDefault()).toEpochSecond();
                this.openZeroTimestamp = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.openTimestamp * 1000), ZoneId.systemDefault())
                        .withHour(0)
                        .withMinute(0)
                        .withSecond(0)
                        .withNano(0)
                        .atZone(ZoneId.systemDefault())
                        .toInstant()
                        .toEpochMilli();
                break;
            case KEY.MERGE_TIME:
                origin = combineTime;
                if (StringUtil.isEmpty(value)) {
                    combineTime = null;
                    this.mergeTimestamp = 0;
                    this.mergeZeroTimeStamp = 0;
                } else {
                    combineTime = LocalDateTime.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                    this.mergeTimestamp = combineTime.atZone(ZoneId.systemDefault()).toEpochSecond();
                    this.mergeZeroTimeStamp = LocalDateTime.ofInstant(Instant.ofEpochMilli(this.mergeTimestamp * 1000), ZoneId.systemDefault())
                            .withHour(0)
                            .withMinute(0)
                            .withSecond(0)
                            .withNano(0)
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .toEpochMilli();
                }
                break;
            case KEY.CHAT_CHECK_MODE:
                origin = this.chatMode;
                try {
                    int i = Integer.parseInt(value);
                    this.chatMode = i;
                    break;
                } catch (Exception e) {
                    log.error("chatMode修改失败，{}", e.getMessage());
                    return null;
                }
            case KEY.PROTOCOL_LIMIT_LOG:
                origin = this.protocolLimitLog;
                try {
                    boolean b = Boolean.parseBoolean(value);
                    this.protocolLimitLog = b;
                    break;
                } catch (Exception e) {
                    log.error("protocol.limit.log修改失败，{}", e.getMessage());
                    return null;
                }
            case KEY.PROTOCOL_LIMIT_LOG_DELAY:
                origin = this.protocolLimitLogDelay;
                try {
                    int i = Integer.parseInt(value);
                    this.protocolLimitLogDelay = i;
                    break;
                } catch (Exception e) {
                    log.error("protocol.limit.log.delay修改失败，{}", e.getMessage());
                    return null;
                }
            case KEY.PROTOCOL_LIMIT_LOG_RATE:
                origin = this.protocolLimitLogRate;
                try {
                    int i = Integer.parseInt(value);
                    this.protocolLimitLogRate = i;
                    break;
                } catch (Exception e) {
                    log.error("protocol.limit.log.rate，{}", e.getMessage());
                    return null;
                }
            case KEY.MATCH_ADDRESS:
                origin = this.matchRemote;

                try {
                    String[] temp = value.split(Symbol.MAOHAO);
                    this.matchRemote = new Remote(temp[0], Integer.parseInt(temp[1]));
                } catch (Exception e) {
                    this.matchRemote = null;
                }
                log.info("匹配服变化#变更：{} -> {}，是否启动时的变化：{}", origin, this.matchRemote, !save);
                break;
            case KEY.MATCH_GROUP:
                origin = this.matchGroup;
                try {
                    int i = Integer.parseInt(value);
                    this.matchGroup = i;
                    log.info("游戏服匹配组别#变更：{} -> {}，是否启动时的变化：{}", origin, this.matchGroup, !save);
                    break;
                } catch (Exception e) {
                    log.error("matchGroup修改失败，{}", e.getMessage());
                    return null;
                }
            case KEY.PLATFORM_RECHARGED_RANK_URL:
                origin = this.platformRechargeRankUrl;
                try {
                    this.platformRechargeRankUrl = value;
                    log.info("系统配置参数，全服充值排行榜配置为：{}", this.platformRechargeRankUrl);
                    break;
                } catch (Exception e) {
                    log.error("全服充值排行榜配置失败，{}", e.getMessage());
                    return null;
                }
            case KEY.MATCH_DFLS_GROUP:
                origin = this.dflsGroup;
                try {
                    int i = Integer.parseInt(value);
                    this.dflsGroup = i;
                    log.info("游戏服巅峰联赛匹配组别#变更：{} -> {}，是否启动时的变化：{}", origin, this.dflsGroup, !save);
                    break;
                } catch (Exception e) {
                    log.error("dflsGroup修改失败，{}", e.getMessage());
                    return null;
                }
            default:
                log.error("unsupported update field. {}", key);
                return null;
        }
        if (save) {
            this.save(key, value);
        }
        return origin;
    }

    /**
     * 读取配置文件
     *
     * @return
     */
    private OrderedProperties read() {
        try (FileInputStream is = new FileInputStream(configPath)) {
            OrderedProperties properties = new OrderedProperties();
            properties.load(is);
            return properties;
        } catch (Exception e) {
            throw new RuntimeException("读取配置失败.", e);
        }
    }

    /**
     * 保存键值对
     *
     * @param key
     * @param value
     */
    public synchronized void save(String key, String value) {
        OrderedProperties properties = read();
        properties.setProperty(key, value);
        try (FileOutputStream os = new FileOutputStream(configPath)) {
            properties.store(os, null);
        } catch (Exception e) {
            throw new RuntimeException("写入配置失败", e);
        }
    }

}
