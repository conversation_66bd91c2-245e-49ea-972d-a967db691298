package com.sh.game.remote.rpc.processor;

import com.sh.concurrent.QueueDriver;
import com.sh.concurrent.QueueExecutor;
import com.sh.game.notice.NoticePool;
import com.sh.game.server.CommandProcessor;


/**
 * ModuleInternProcessor
 *
 * <AUTHOR>
 * @date 2020/8/27 17:15
 */
public class ModuleInternProcessor extends CommandProcessor {
    private final QueueDriver driver =  new QueueDriver(new QueueExecutor("module-internal-thread", 1, 1),
            "notice和msg通信转发线程", 0, 5000);

    @Override
    public byte id() {
        return 22;
    }

    @Override
    protected QueueDriver getDriver(long key) {
        return driver;
    }

    @Override
    protected NoticePool getNoticePool() {
        throw new UnsupportedOperationException("notice和msg通信转发线程不支持notice处理");
    }

}
