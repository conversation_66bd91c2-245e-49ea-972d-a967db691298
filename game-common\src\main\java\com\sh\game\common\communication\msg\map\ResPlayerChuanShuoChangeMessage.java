package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>玩家传说变化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPlayerChuanShuoChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange proto;

    private com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange.Builder builder;

	
	@Override
	public int getId() {
		return 67049;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResPlayerChuanShuoChange proto) {
        this.proto = proto;
    }

}
