package com.sh.game.common.communication.msg.map.duplicate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>同步副本目标信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDuplicateGoalInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo proto;

    private com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo.Builder builder;

	
	@Override
	public int getId() {
		return 71054;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DuplicateProtos.ResDuplicateGoalInfo proto) {
        this.proto = proto;
    }

}
