package com.sh.game.common.communication.msg.pvp;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>注册服务器返回消息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResRegisterServerMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 125002;
	}
	
	/**
	 * 主机
	 */
	private int hostId;
	/**
	 * IP地址
	 */
	private String ip;
	/**
	 * 端口
	 */
	private int port;
	/**
	 * 注册时间
	 */
	private int time;

	public int getHostId() {
		return hostId;
	}

	public void setHostId(int hostId) {
		this.hostId = hostId;
	}

		public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

		public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

		public int getTime() {
		return time;
	}

	public void setTime(int time) {
		this.time = time;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.hostId = readInt(buf, false);
		this.ip = readString(buf);
		this.port = readInt(buf, false);
		this.time = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, hostId, false);
		this.writeString(buf, ip);
		this.writeInt(buf, port, false);
		this.writeInt(buf, time, false);
		return true;
	}
}
