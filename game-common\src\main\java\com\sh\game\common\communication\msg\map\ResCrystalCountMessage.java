package com.sh.game.common.communication.msg.map;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>跨服挖水晶玩法当日剩余次数</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResCrystalCountMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 67065;
	}
	
	/**
	 * 剩余次数
	 */
	private int leftCount;

	public int getLeftCount() {
		return leftCount;
	}

	public void setLeftCount(int leftCount) {
		this.leftCount = leftCount;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.leftCount = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, leftCount, false);
		return true;
	}
}
