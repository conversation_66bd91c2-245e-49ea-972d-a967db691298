package com.sh.game.notice;

import com.sh.concurrent.AbstractCommand;

/**
 * NoticeCallback
 *
 * <AUTHOR>
 * @date 2020/8/18 17:37
 */
public abstract class NoticeCallback<T extends ProcessNotice> extends AbstractCommand {

    T notice;

    @Override
    public void doAction() {
        callback(notice);
    }

    public T getNotice() {
        return notice;
    }

    public void setNotice(T notice) {
        this.notice = notice;
    }

    public abstract void callback(T notice);
}
